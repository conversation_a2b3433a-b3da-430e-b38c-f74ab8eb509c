/**
 * 粉笔题目获取API
 * 迁移自egg项目的fb.js fbtimu方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    
    // 解析参数
    const per = parseInt(searchParams.get('per')) || 10;
    const page = parseInt(searchParams.get('page')) || 1;
    const id = searchParams.get('id');
    const type = searchParams.get('type') || 'syzc';
    const z = searchParams.get('z');
    const b = searchParams.get('b');
    const f = searchParams.get('f');
    const o = searchParams.get('o');
    const q = searchParams.get('q');
    const ids = searchParams.get('ids');
    const biao = searchParams.get('biao') || 'fbsy';
    const t = searchParams.get('t');
    const zql = searchParams.get('zql');
    const kjid = searchParams.get('kjid');
    const parentid = searchParams.get('parentid');
    const fast = searchParams.get('fast');
    
    console.log('📚 获取题目数据 - 参数:', {
      per, page, id, type, biao, kjid, fast
    });
    
    // 构建查询条件
    const where = {};
    const options = {
      where,
      limit: per,
      offset: (page - 1) * per,
      orders: [['id', 'ASC']]
    };
    
    // 根据参数构建查询条件
    if (id) {
      where.id = id;
    }
    
    if (ids) {
      // 处理多个ID查询
      const idList = ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      if (idList.length > 0) {
        // 使用原生SQL查询多个ID
        const sql = `SELECT * FROM ${biao} WHERE id IN (${idList.map(() => '?').join(',')}) ORDER BY id ASC`;
        const data = await app.mysql.query(sql, idList);
        
        // 直接返回数组，与egg项目格式保持一致
        return NextResponse.json(data);
      }
    }
    
    if (parentid) {
      where.parentid = parentid;
    }
    
    if (zql) {
      // 正确率查询
      where.correctRatio = zql;
    }
    
    if (q) {
      // 模糊查询内容
      const sql = `SELECT * FROM ${biao} WHERE content LIKE ? OR solution LIKE ? ORDER BY id ASC LIMIT ? OFFSET ?`;
      const searchTerm = `%${q}%`;
      const data = await app.mysql.query(sql, [searchTerm, searchTerm, per, (page - 1) * per]);
      
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM ${biao} WHERE content LIKE ? OR solution LIKE ?`;
      const countResult = await app.mysql.query(countSql, [searchTerm, searchTerm]);
      const total = countResult[0].total;
      
      // 直接返回数组，与egg项目格式保持一致
      return NextResponse.json(data);
    }
    
    // 执行查询
    const data = await app.mysql.select(biao, options);
    
    // 获取总数
    const countResult = await app.mysql.query(`SELECT COUNT(*) as total FROM ${biao}${Object.keys(where).length > 0 ? ' WHERE ' + Object.keys(where).map(key => `${key} = ?`).join(' AND ') : ''}`, Object.values(where));
    const total = countResult[0].total;
    
    console.log('✅ 题目数据获取成功:', {
      count: data.length,
      total,
      page,
      per
    });
    
    // 直接返回数组，与egg项目格式保持一致
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('❌ fbtimu API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
