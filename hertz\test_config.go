package main

import (
	"fmt"
	"log"

	"hertz-demo/pkg/config"
)

func main() {
	fmt.Println("=== 测试配置系统 ===")

	// 测试加载dev配置
	fmt.Println("\n1. 测试加载dev配置:")
	devCfg, err := config.LoadConfig("dev")
	if err != nil {
		log.Fatalf("加载dev配置失败: %v", err)
	}
	fmt.Printf("✅ Dev配置加载成功\n")
	fmt.Printf("   MySQL: %s:%d/%s\n", devCfg.MySQL.Host, devCfg.MySQL.Port, devCfg.MySQL.DB)
	fmt.Printf("   Redis: %s:%d/%d\n", devCfg.Redis.Host, devCfg.Redis.Port, devCfg.Redis.DB)
	fmt.Printf("   Server: %s:%d\n", devCfg.Server.Host, devCfg.Server.Port)
	fmt.Printf("   Log: %s/%s\n", devCfg.Log.Level, devCfg.Log.Format)

	// 测试加载prod配置
	fmt.Println("\n2. 测试加载prod配置:")
	prodCfg, err := config.LoadConfig("prod")
	if err != nil {
		log.Fatalf("加载prod配置失败: %v", err)
	}
	fmt.Printf("✅ Prod配置加载成功\n")
	fmt.Printf("   MySQL: %s:%d/%s\n", prodCfg.MySQL.Host, prodCfg.MySQL.Port, prodCfg.MySQL.DB)
	fmt.Printf("   Redis: %s:%d/%d\n", prodCfg.Redis.Host, prodCfg.Redis.Port, prodCfg.Redis.DB)
	fmt.Printf("   Server: %s:%d\n", prodCfg.Server.Host, prodCfg.Server.Port)
	fmt.Printf("   Log: %s/%s\n", prodCfg.Log.Level, prodCfg.Log.Format)

	// 测试DSN生成
	fmt.Println("\n3. 测试DSN生成:")
	mysqlDSN := devCfg.MySQL.GetMySQLDSN()
	redisAddr := devCfg.Redis.GetRedisAddr()
	fmt.Printf("✅ MySQL DSN: %s\n", mysqlDSN)
	fmt.Printf("✅ Redis Addr: %s\n", redisAddr)

	// 测试配置验证
	fmt.Println("\n4. 测试配置验证:")
	fmt.Printf("✅ 配置验证通过\n")

	// 测试全局配置获取
	fmt.Println("\n5. 测试全局配置:")
	globalCfg := config.GetConfig()
	fmt.Printf("✅ 全局配置获取成功: %s环境\n", 
		func() string {
			if globalCfg.MySQL.Host == "prod.db.server" {
				return "prod"
			}
			return "dev"
		}())

	fmt.Println("\n=== 配置系统测试完成 ===")
}
