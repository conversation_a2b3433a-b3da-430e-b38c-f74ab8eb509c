package config

import (
	"os"
	"testing"
)

func TestLoadConfig(t *testing.T) {
	// 测试加载dev配置
	cfg, err := LoadConfig("dev")
	if err != nil {
		t.Fatalf("加载dev配置失败: %v", err)
	}

	// 验证MySQL配置
	if cfg.MySQL.Host != "************" {
		t.<PERSON><PERSON><PERSON>("期望MySQL host为************，实际为%s", cfg.MySQL.Host)
	}
	if cfg.MySQL.Port != 11436 {
		t.<PERSON>rrorf("期望MySQL port为11436，实际为%d", cfg.MySQL.Port)
	}
	if cfg.MySQL.User != "root" {
		t.<PERSON><PERSON>("期望MySQL user为root，实际为%s", cfg.MySQL.User)
	}
	if cfg.MySQL.DB != "zz" {
		t.<PERSON>rrorf("期望MySQL db为zz，实际为%s", cfg.MySQL.DB)
	}

	// 验证Redis配置
	if cfg.Redis.Host != "************" {
		t.<PERSON><PERSON><PERSON>("期望Redis host为************，实际为%s", cfg.Redis.Host)
	}
	if cfg.Redis.Port != 2639 {
		t.Errorf("期望Redis port为2639，实际为%d", cfg.Redis.Port)
	}
	if cfg.Redis.DB != 0 {
		t.Errorf("期望Redis db为0，实际为%d", cfg.Redis.DB)
	}

	// 验证服务器配置（应该有默认值或配置值）
	if cfg.Server.Host != "127.0.0.1" {
		t.Errorf("期望Server host为127.0.0.1，实际为%s", cfg.Server.Host)
	}
	if cfg.Server.Port != 8080 {
		t.Errorf("期望Server port为8080，实际为%d", cfg.Server.Port)
	}

	// 验证日志配置
	if cfg.Log.Level != "debug" {
		t.Errorf("期望Log level为debug，实际为%s", cfg.Log.Level)
	}
}

func TestLoadConfigProd(t *testing.T) {
	// 测试加载prod配置
	cfg, err := LoadConfig("prod")
	if err != nil {
		t.Fatalf("加载prod配置失败: %v", err)
	}

	// 验证MySQL配置
	if cfg.MySQL.Host != "prod.db.server" {
		t.Errorf("期望MySQL host为prod.db.server，实际为%s", cfg.MySQL.Host)
	}
	if cfg.MySQL.Port != 3306 {
		t.Errorf("期望MySQL port为3306，实际为%d", cfg.MySQL.Port)
	}

	// 验证Redis配置
	if cfg.Redis.Host != "prod.redis.server" {
		t.Errorf("期望Redis host为prod.redis.server，实际为%s", cfg.Redis.Host)
	}
	if cfg.Redis.Port != 6379 {
		t.Errorf("期望Redis port为6379，实际为%d", cfg.Redis.Port)
	}

	// 验证服务器配置
	if cfg.Server.Host != "0.0.0.0" {
		t.Errorf("期望Server host为0.0.0.0，实际为%s", cfg.Server.Host)
	}

	// 验证日志配置
	if cfg.Log.Level != "info" {
		t.Errorf("期望Log level为info，实际为%s", cfg.Log.Level)
	}
	if cfg.Log.Format != "json" {
		t.Errorf("期望Log format为json，实际为%s", cfg.Log.Format)
	}
}

func TestGetMySQLDSN(t *testing.T) {
	cfg := &MySQLConfig{
		Host:     "localhost",
		Port:     3306,
		User:     "root",
		Password: "password",
		DB:       "testdb",
	}

	expected := "root:password@tcp(localhost:3306)/testdb?charset=utf8mb4&parseTime=True&loc=Local"
	actual := cfg.GetMySQLDSN()

	if actual != expected {
		t.Errorf("期望DSN为%s，实际为%s", expected, actual)
	}
}

func TestGetRedisAddr(t *testing.T) {
	cfg := &RedisConfig{
		Host: "localhost",
		Port: 6379,
	}

	expected := "localhost:6379"
	actual := cfg.GetRedisAddr()

	if actual != expected {
		t.Errorf("期望Redis地址为%s，实际为%s", expected, actual)
	}
}

func TestValidateConfig(t *testing.T) {
	// 测试有效配置
	validConfig := &Config{
		MySQL: MySQLConfig{
			Host: "localhost",
			Port: 3306,
			User: "root",
			DB:   "testdb",
		},
		Redis: RedisConfig{
			Host: "localhost",
			Port: 6379,
			DB:   0,
		},
		Server: ServerConfig{
			Port: 8080,
		},
	}

	if err := validateConfig(validConfig); err != nil {
		t.Errorf("有效配置验证失败: %v", err)
	}

	// 测试无效配置 - MySQL host为空
	invalidConfig := &Config{
		MySQL: MySQLConfig{
			Port: 3306,
			User: "root",
			DB:   "testdb",
		},
		Redis: RedisConfig{
			Host: "localhost",
			Port: 6379,
			DB:   0,
		},
		Server: ServerConfig{
			Port: 8080,
		},
	}

	if err := validateConfig(invalidConfig); err == nil {
		t.Error("期望配置验证失败，但验证通过了")
	}
}

func TestEnvironmentVariables(t *testing.T) {
	// 设置环境变量
	os.Setenv("HERTZ_ENV", "prod")
	defer os.Unsetenv("HERTZ_ENV")

	// 不传入环境参数，应该使用环境变量
	cfg, err := LoadConfig()
	if err != nil {
		t.Fatalf("使用环境变量加载配置失败: %v", err)
	}

	// 应该加载prod配置
	if cfg.MySQL.Host != "prod.db.server" {
		t.Errorf("期望使用prod配置，但MySQL host为%s", cfg.MySQL.Host)
	}
}
