package main

import (
	"flag"
	"fmt"
	"io/ioutil"
	"os"

	"gopkg.in/yaml.v3"
)

type MySQLConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DB       string `yaml:"db"`
}

type RedisConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

type Config struct {
	MySQL MySQLConfig `yaml:"mysql"`
	Redis RedisConfig `yaml:"redis"`
}

func LoadConfig() (*Config, error) {
	var env string
	flag.StringVar(&env, "env", "", "环境(dev或prod)，优先于环境变量HERTZ_ENV")
	flag.Parse()
	if env == "" {
		env = os.Getenv("HERTZ_ENV")
	}
	if env == "" {
		env = "dev"
	}
	var configFile string
	if env == "prod" {
		configFile = "config/config.prod.yaml"
	} else {
		configFile = "config/config.dev.yaml"
	}
	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}
	return &cfg, nil
}

// 示例main函数
func main() {
	cfg, err := LoadConfig()
	if err != nil {
		fmt.Println("配置加载失败:", err)
		os.Exit(1)
	}
	fmt.Printf("当前环境配置: %+v\n", cfg)

	// 测试数据库连接
	db, err := InitDB(cfg)
	if err != nil {
		fmt.Println("❌ 数据库初始化失败:", err)
	} else if err := TestDBPing(db); err != nil {
		fmt.Println("❌ 数据库连接失败:", err)
	} else {
		fmt.Println("✅ 数据库连接成功！")
	}

	// 测试Redis连接
	_, err = InitRedis(cfg)
	if err != nil {
		fmt.Println("❌ Redis初始化失败:", err)
	} else {
		fmt.Println("✅ Redis连接成功！")
	}
}
