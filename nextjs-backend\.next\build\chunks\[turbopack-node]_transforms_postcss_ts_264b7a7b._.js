module.exports = {

"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/nextjs-backend/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/c0cd7_316d10ea._.js",
  "build/chunks/[root-of-the-server]__3d8918c3._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/nextjs-backend/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),

};