# 📁 Hertz vs Eggjs 目录结构对比

## 🥚 Eggjs 目录结构

- app/           🍳 业务代码（controller、service、middleware等）
- config/        ⚙️ 配置文件（环境、插件等）
- logs/          📜 日志目录
- scripts/       🛠️ 脚本工具
- typings/       📚 TypeScript类型定义
- node_modules/  📦 依赖包
- run/           🚀 运行时文件
- rust/          🦀 Rust相关
- nextjs/        ⚡ Next.js相关
- .vscode/       🖥️ VSCode配置
- .github/       🐙 GitHub配置
- ...            其他辅助文件

## ⚡ Hertz 目录结构

- config/        ⚙️ 配置文件（dev/prod环境yaml）
- db.go          🗄️ 数据库初始化（GORM）
- redis.go       🧩 Redis初始化（go-redis）
- config.go      🧾 配置加载与环境切换
- go.mod/go.sum  📦 Go依赖管理

## 🔍 主要区别与说明

- Eggjs结构更偏向MVC，业务与配置分层细致，适合大型Node项目。
- Hertz当前为Go微服务后端基础骨架，结构简洁，聚焦配置与数据层初始化。
- Eggjs有丰富的脚本、类型、前端集成，Hertz更轻量，适合高性能API服务。

---

✨ 未来可根据业务需求，逐步扩展hertz的app、middleware、service等目录，实现更完整的分层架构！ 🚀