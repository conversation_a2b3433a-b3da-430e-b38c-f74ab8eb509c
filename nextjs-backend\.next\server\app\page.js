const CHUNK_PUBLIC_PATH = "server/app/page.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/c0cd7_7a7e2fc1._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d8593af1._.js");
runtime.loadChunk("server/chunks/ssr/nextjs-backend_src_app_7e581bd1._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__e85c5e6c._.js");
runtime.loadChunk("server/chunks/ssr/c0cd7_next_dist_client_components_ae4060a8._.js");
runtime.loadChunk("server/chunks/ssr/c0cd7_next_dist_client_components_builtin_forbidden_092fd70a.js");
runtime.loadChunk("server/chunks/ssr/c0cd7_next_dist_client_components_builtin_unauthorized_f273f2f8.js");
runtime.loadChunk("server/chunks/ssr/c0cd7_next_dist_client_components_builtin_global-error_21fdc3b0.js");
runtime.loadChunk("server/chunks/ssr/c0cd7_next_dist_a34a5e5a._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__82222473._.js");
runtime.getOrInstantiateRuntimeModule("[project]/nextjs-backend/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/nextjs-backend/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/nextjs-backend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/nextjs-backend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/nextjs-backend/src/app/layout.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/nextjs-backend/src/app/page.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/nextjs-backend/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/nextjs-backend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/nextjs-backend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/nextjs-backend/src/app/layout.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/nextjs-backend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/nextjs-backend/src/app/page.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
