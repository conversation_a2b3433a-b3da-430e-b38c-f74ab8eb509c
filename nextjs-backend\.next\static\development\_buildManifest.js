self.__BUILD_MANIFEST = (function(a,b,c,d,e,f,g,h,i,j){return {__rewrites:{afterFiles:[{has:a,source:"\u002Ffbtimu",destination:b},{has:a,source:"\u002Ffbgettimu",destination:b},{has:a,source:"\u002Ffbgethtimu",destination:c},{has:a,source:"\u002Fgethtimu",destination:c},{has:a,source:"\u002Fpinglun",destination:d},{has:a,source:"\u002Ffbpl",destination:d},{has:a,source:"\u002Fgettimurank",destination:e},{has:a,source:"\u002Ffbsubmit",destination:f},{has:a,source:"\u002Ffbremeber",destination:g},{has:a,source:"\u002Ffbrecoverpage",destination:h},{has:a,source:"\u002Fsetnull",destination:i},{has:a,source:"\u002Fupdateidsnull",destination:j},{has:a,source:"\u002Fnext\u002Ffbtimu",destination:b},{has:a,source:"\u002Fnext\u002Ffbgettimu",destination:b},{has:a,source:"\u002Fnext\u002Ffbgethtimu",destination:c},{has:a,source:"\u002Fnext\u002Fgethtimu",destination:c},{has:a,source:"\u002Fnext\u002Fpinglun",destination:d},{has:a,source:"\u002Fnext\u002Ffbpl",destination:d},{has:a,source:"\u002Fnext\u002Fgettimurank",destination:e},{has:a,source:"\u002Fnext\u002Ffbsubmit",destination:f},{has:a,source:"\u002Fnext\u002Ffbremeber",destination:g},{has:a,source:"\u002Fnext\u002Ffbrecoverpage",destination:h},{has:a,source:"\u002Fnext\u002Fsetnull",destination:i},{has:a,source:"\u002Fnext\u002Fupdateidsnull",destination:j},{has:a,source:"\u002Fshowip",destination:"\u002Fapi\u002Fhome\u002Fshowip"},{has:a,source:"\u002Fgitpull",destination:"\u002Fapi\u002Fhome\u002Fgitpull"},{has:a,source:"\u002Fbutton",destination:"\u002Fapi\u002Fhome\u002Fbutton"},{has:a,source:"\u002Fclashsw",destination:"\u002Fapi\u002Fhome\u002Fclashsw"},{has:a,source:"\u002Fhealth",destination:"\u002Fapi\u002Fhome\u002Fhealth"},{has:a,source:"\u002Ftest-db",destination:"\u002Fapi\u002Ftest-db"},{has:a,source:"\u002Fwebsocket-test",destination:"\u002Fapi\u002Fwebsocket\u002Ftest"}],beforeFiles:[],fallback:[]},__routerFilterStatic:a,__routerFilterDynamic:a,sortedPages:["\u002F_app"]}}(void 0,"\u002Fapi\u002Ffb\u002Ftimu","\u002Fapi\u002Ffb\u002Fgethtimu","\u002Fapi\u002Ffb\u002Fpinglun","\u002Fapi\u002Ffb\u002Fgettimurank","\u002Fapi\u002Ffb\u002Ffbsubmit","\u002Fapi\u002Ffb\u002Ffbremeber","\u002Fapi\u002Ffb\u002Ffbrecoverpage","\u002Fapi\u002Ffb\u002Fsetnull","\u002Fapi\u002Ffb\u002Fupdateidsnull"));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()