self.__BUILD_MANIFEST = (function(a,b,c,d){return {__rewrites:{afterFiles:[{has:a,source:"\u002Ffbtimu",destination:b},{has:a,source:"\u002Ffbgettimu",destination:b},{has:a,source:"\u002Ffbgethtimu",destination:c},{has:a,source:"\u002Fgethtimu",destination:c},{has:a,source:"\u002Fpinglun",destination:d},{has:a,source:"\u002Ffbpl",destination:d},{has:a,source:"\u002Fgettimurank",destination:"\u002Fapi\u002Ffb\u002Fgettimurank"},{has:a,source:"\u002Ffbsubmit",destination:"\u002Fapi\u002Ffb\u002Ffbsubmit"},{has:a,source:"\u002Ffbremeber",destination:"\u002Fapi\u002Ffb\u002Ffbremeber"},{has:a,source:"\u002Ffbrecoverpage",destination:"\u002Fapi\u002Ffb\u002Ffbrecoverpage"},{has:a,source:"\u002Fsetnull",destination:"\u002Fapi\u002Ffb\u002Fsetnull"},{has:a,source:"\u002Fupdateidsnull",destination:"\u002Fapi\u002Ffb\u002Fupdateidsnull"},{has:a,source:"\u002Fshowip",destination:"\u002Fapi\u002Fhome\u002Fshowip"},{has:a,source:"\u002Fgitpull",destination:"\u002Fapi\u002Fhome\u002Fgitpull"},{has:a,source:"\u002Fbutton",destination:"\u002Fapi\u002Fhome\u002Fbutton"},{has:a,source:"\u002Fclashsw",destination:"\u002Fapi\u002Fhome\u002Fclashsw"},{has:a,source:"\u002Fhealth",destination:"\u002Fapi\u002Fhome\u002Fhealth"},{has:a,source:"\u002Ftest-db",destination:"\u002Fapi\u002Ftest-db"},{has:a,source:"\u002Fwebsocket-test",destination:"\u002Fapi\u002Fwebsocket\u002Ftest"}],beforeFiles:[],fallback:[]},__routerFilterStatic:a,__routerFilterDynamic:a,sortedPages:["\u002F_app"]}}(void 0,"\u002Fapi\u002Ffb\u002Ftimu","\u002Fapi\u002Ffb\u002Fgethtimu","\u002Fapi\u002Ffb\u002Fpinglun"));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()