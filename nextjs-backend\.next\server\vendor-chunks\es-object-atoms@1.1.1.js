"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/es-object-atoms@1.1.1";
exports.ids = ["vendor-chunks/es-object-atoms@1.1.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/es-object-atoms@1.1.1/node_modules/es-object-atoms/index.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/es-object-atoms@1.1.1/node_modules/es-object-atoms/index.js ***!
  \****************************************************************************************/
/***/ ((module) => {

eval("\n\n/** @type {import('.')} */\nmodule.exports = Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZXMtb2JqZWN0LWF0b21zQDEuMS4xL25vZGVfbW9kdWxlcy9lcy1vYmplY3QtYXRvbXMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsV0FBVyxhQUFhO0FBQ3hCIiwic291cmNlcyI6WyJJOlxcd29ya3NwYWNlXFxuZXh0anMtYmFja2VuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZXMtb2JqZWN0LWF0b21zQDEuMS4xXFxub2RlX21vZHVsZXNcXGVzLW9iamVjdC1hdG9tc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKiogQHR5cGUge2ltcG9ydCgnLicpfSAqL1xubW9kdWxlLmV4cG9ydHMgPSBPYmplY3Q7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/es-object-atoms@1.1.1/node_modules/es-object-atoms/index.js\n");

/***/ })

};
;