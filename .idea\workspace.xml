<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ec71efca-5f0b-4c5f-b1ab-01971d06db6e" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/egg/vue/src/views/fb/sy.vue" beforeDir="false" afterPath="$PROJECT_DIR$/egg/vue/src/views/fb/sy.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/egg/vue/vite.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/egg/vue/vite.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nextjs-backend/next.config.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/nextjs-backend/package-lock.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/nextjs-backend/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/nextjs-backend/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nextjs-backend/src/app/layout.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/nextjs-backend/src/app/page.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/nextjs-backend/tsconfig.json" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/egg/vue" />
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/egg/nextjs/package.json" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="30XgD4vyIcprxmbeA5YL7atbVPO" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.dev.executor": "Run",
    "npm.dev:all.executor": "Run",
    "settings.editor.selected.configurable": "project.propVCSSupport.DirectoryMappings",
    "ts.external.directory.path": "I:\\workspace\\nextjs-backend\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="dev:all" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/egg/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev:all" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-WS-251.23774.424" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ec71efca-5f0b-4c5f-b1ab-01971d06db6e" name="更改" comment="" />
      <created>1753779209071</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753779209071</updated>
      <workItem from="1753779211096" duration="234000" />
      <workItem from="1753844626023" duration="942000" />
      <workItem from="1753863221753" duration="1202000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>