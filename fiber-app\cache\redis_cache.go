package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisCache struct {
	client *redis.Client
}

func NewRedisCache(addr, password string, db int) *RedisCache {
	rdb := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})
	return &RedisCache{client: rdb}
}

// SetEx sets a key-value pair with an expiration time.
func (c *RedisCache) SetEx(key string, value interface{}, expiration time.Duration) error {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}
	return c.client.SetEx(ctx, key, data, expiration).Err()
}

// Get retrieves a value by key and unmarshals it into the provided destination.
func (c *RedisCache) Get(key string, dest interface{}) error {
	ctx := context.Background()
	val, err := c.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return fmt.Errorf("key not found: %s", key)
	} else if err != nil {
		return fmt.Errorf("failed to get value from redis: %w", err)
	}
	return json.Unmarshal([]byte(val), dest)
}

// Del deletes one or more keys.
func (c *RedisCache) Del(keys ...string) error {
	ctx := context.Background()
	return c.client.Del(ctx, keys...).Err()
}

// Ping checks the connectivity to the Redis server.
func (c *RedisCache) Ping() error {
	ctx := context.Background()
	return c.client.Ping(ctx).Err()
}
