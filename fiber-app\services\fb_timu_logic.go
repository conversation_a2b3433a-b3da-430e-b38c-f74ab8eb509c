package services

import (
	"fiber-app/models"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// ProcessTimuLogic contains the core business logic for the 'timu' functionality.
// It builds the SQL query dynamically, fetches data, and processes it.
func (s *FBService) ProcessTimuLogic(params models.TimuParams) (map[string]interface{}, error) {
	// This is the Go equivalent of the logic in egg/app/controller/fb.js async timu()

	// 1. Initialize variables
	var (
		sqlquery string
		// Other variables from JS
	)

	// Use a strings.Builder for efficient SQL string construction
	var queryBuilder strings.Builder
	var args []interface{}

	// 2. Replicate the main logic flow from fb.js
	fastMode, _ := strconv.ParseBool(params.Fast)
	z, _ := strconv.Atoi(params.Z)

	if z == 1 {
		// Logic for z == 1 (Material Analysis)
		// ... to be implemented
		queryBuilder.WriteString("SELECT ... FROM material_analysis WHERE ...")
	} else {
		// Main logic branch
		biao := "timu"
		if params.Biao != "" {
			biao = params.Biao
		}

		fields := "id, question, answer, conosco, options, type, zsd, nandu, zql, addtime, ren, bz, parentid, f, o, b, z, t"
		if fastMode {
			fields = "id, answer, conosco, zsd, nandu, zql, addtime, ren, bz, parentid, f, o, b, z, t"
		}

		queryBuilder.WriteString(fmt.Sprintf("SELECT %s FROM %s WHERE del = 0 ", fields, biao))

		// WHERE clause construction based on parameters
		if params.ID != "" {
			queryBuilder.WriteString("AND id = ? ")
			args = append(args, params.ID)
		}
		if params.ParentID != "" {
			queryBuilder.WriteString("AND parentid = ? ")
			args = append(args, params.ParentID)
		}
		if params.F != "" {
			queryBuilder.WriteString("AND f = ? ")
			args = append(args, params.F)
		}
		if params.O != "" {
			queryBuilder.WriteString("AND o = ? ")
			args = append(args, params.O)
		}
		if params.B != "" {
			queryBuilder.WriteString("AND b = ? ")
			args = append(args, params.B)
		}
		if params.Z != "" {
			queryBuilder.WriteString("AND z = ? ")
			args = append(args, params.Z)
		}
		if params.T != "" {
			queryBuilder.WriteString("AND t = ? ")
			args = append(args, params.T)
		}
		if params.Q != "" {
			// In JS: ` and question like '%${q}%' `
			queryBuilder.WriteString("AND question LIKE ? ")
			args = append(args, "%"+params.Q+"%")
		}
		if params.IDs != "" {
			// In JS: ` and id in (${ids}) `
			// Assuming ids is a comma-separated string of numbers.
			// We need to create a placeholder for each id.
			idList := strings.Split(params.IDs, ",")
			if len(idList) > 0 {
				queryBuilder.WriteString("AND id IN (?" + strings.Repeat(",?", len(idList)-1) + ") ")
				for _, id := range idList {
					args = append(args, id)
				}
			}
		}
	}

	// 3. Finalize query (ORDER BY, LIMIT, etc.)
	if params.Kjid != "" {
		// In JS: `order by CONVERT(answer USING gbk) asc`
		queryBuilder.WriteString("ORDER BY CONVERT(answer USING gbk) ASC ")
	} else if params.ID == "" {
		// In JS: `order by id desc`
		queryBuilder.WriteString("ORDER BY id DESC ")
	}

	// Pagination
	if params.Per != "" && params.Page != "" {
		per, _ := strconv.Atoi(params.Per)
		page, _ := strconv.Atoi(params.Page)
		if per > 0 && page > 0 {
			offset := (page - 1) * per
			queryBuilder.WriteString(fmt.Sprintf("LIMIT %d OFFSET %d ", per, offset))
		}
	}

	sqlquery = queryBuilder.String()

	// Execute the query using the repository
	results, err := s.repo.Select(sqlquery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute timu query: %w", err)
	}

	// Post-processing the results
	for _, row := range results {
		// Format answer options
		if options, ok := row["options"].(string); ok {
			row["options"] = formatAnswerOptions(options)
		}

		// TODO: Format 'addtime' if necessary
		// The default format from the DB might be sufficient.
		// If specific formatting like 'YYYY-MM-DD' is needed, it will be handled here.
	}

	// Return the processed results
	return map[string]interface{}{
		"status": "success",
		"data":   results,
		// "count": count, // Will be added later
	}, nil
}

// Helper functions from fb.js will be translated here
// e.g., formatAnswerOptions, dateformat

// formatAnswerOptions replicates the JavaScript function:
// str.replace(/([A-Z])\./g, '<br>$1.');
func formatAnswerOptions(options string) string {
	if options == "" {
		return ""
	}
	re := regexp.MustCompile(`([A-Z])\.`)
	return re.ReplaceAllString(options, "<br>$1.")
}

func dateformat(date time.Time, format string) string {
	// TODO: Translate the logic from fb.js
	return date.Format(format) // Placeholder
}
