<!--sy.vue-->

<script setup>
  import { useCanvasStore } from '@/store/canvas';
  import { useExerciseStore } from '@/store/exercise'; // 引入 Exercise Store
  import { useLoadingStore } from '@/store/loading'; // 引入 Loading Store
  import { useThemeStore } from '@/store/theme';
  // import rankLoader from '@/utils/rankLoader'; // 🚀 引入智能排名加载器
  import { message } from 'ant-design-vue';
  import axios from 'axios';
  import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';

  const data = ref([]);
  // const isLoading = ref(true); // 移除：现在由 loadingStore 管理
  const current_type = ref('sy');
  const showTop = ref(true);
  const current_page = ref(1);
  const perpage = ref(1);
  const total = ref(0);
  const route = useRoute();
  const isTagHidden = ref(true);
  const isAnswerHighlighted = ref(true); // 默认显示答案高亮，原 showdaan
  const showdaanblock = ref('');
  const resolution = ref('');
  const open_count = ref(0);
  const daanblock = ref(true);
  const showvideo = ref(false);
  const showcomment = ref(true);
  const zuotivideo = ref(false);
  const anstype = ref(true);
  const showds = ref(true);
  const grid = ref(window.innerWidth > 768);
  const tid = ref(0);
  const biao = ref('');
  const showbtn = ref(true);
  const gendata = ref([]);
  const rank = ref('');
  const idslist = ref([]);
  const updateidslist = ref([]);
  const screenWidth = ref(window.innerWidth);
  const alltimu = ref(false);
  const maodianindex = ref(0);
  const kjid = ref(route?.query?.kjid);
  const kjids = ref([]);
  const currentQuestionSubmitted = ref(false); // 跟踪当前题目是否已提交成功
  const themeStore = useThemeStore();

  const canvasStore = useCanvasStore();
  const loadingStore = useLoadingStore(); // 获取 Loading Store 实例
  const exerciseStore = useExerciseStore(); // 获取 Exercise Store 实例

  const current_id = computed(() => canvasStore.currentId);

  const zuotimode = () => {
    exerciseStore.toggleZuotiMode(); // 调用 Exercise Store 的方法
  };
  const submit = async () => {
    const urlx = '/next/fbsubmit';
    const type = route.query.type;

    const response = await axios.post(urlx, {
      kjid: route.query.kjid,
      mode: type,
    });
    if (+response.status === 200) {
      message.success({
        content: `提交成功`,
        style: {
          marginTop: '80vh',
        },
      });
    }
  };
  const refreshPage = () => {
    window.location.reload();
  };

  const scrollToTop = async () => {
    if (grid.value) {
      // 桌面端：滚动主窗口和所有 scroll-container
      window.scrollTo({ top: 0, behavior: 'smooth' });
      document.querySelectorAll('.scroll-container').forEach((el) => {
        el.scrollTo ? el.scrollTo({ top: 0, behavior: 'smooth' }) : (el.scrollTop = 0);
      });
    } else {
      // 移动端：主要滚动 .wp 容器，同时也滚动主窗口
      const wpContainer = document.querySelector('.wp');
      if (wpContainer) {
        wpContainer.scrollTo({ top: 0, behavior: 'smooth' });
      }
      // 也滚动主窗口，以防万一
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };
  const setnull = async () => {
    let params = {
      id: tid.value,
      type: current_type.value,
      biao: route.query.biao || 'fbsy',
    };
    console.log('setnull', tid);
    await axios.get('/next/setnull', { params });
    await getData();
  };
  const allshow = async () => {
    alltimu.value = true;
    current_page.value = 1;
    perpage.value = +total.value;
    await getData();
  };
  const getResolution = () => {
    resolution.value = `${window.innerWidth} x ${window.innerHeight}`;
  };
  const toggleHiddenTag = () => {
    // 使用 document.querySelectorAll 获取所有具有 class="tag" 的元素
    const elements = document.querySelectorAll('.tag');

    // 使用 forEach 方法遍历元素，并根据状态变量切换它们的显示/隐藏状态
    elements.forEach((element) => {
      if (isTagHidden.value) {
        element.style.display = 'block'; // 或者其他适当的显示样式
      } else {
        element.style.display = 'none';
      }
    });

    // 切换状态变量
    isTagHidden.value = !isTagHidden.value;
  };
  const open_comment = (id) => {
    // window.open(
    //   '/fb/comment?id=' + id,
    //   '_blank',
    //   'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600',
    // );
    // showcomment.value = !showcomment.value;
  };
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const toggledaanblock = () => {
    daanblock.value = !daanblock.value;
  };

  // 🚀 简化版异步排名加载器
  const pendingRankRequests = new Map(); // 存储正在进行的请求

  const loadRankAsync = async (questionId, allcateid, itemIndex) => {
    console.log('🚀 开始异步加载排名:', questionId);

    const requestKey = `${questionId}_${allcateid}`;

    // 取消之前的请求
    if (pendingRankRequests.has(requestKey)) {
      console.log('🚫 取消之前的排名请求');
      pendingRankRequests.get(requestKey).abort();
    }

    // 创建新的请求控制器
    const abortController = new AbortController();
    pendingRankRequests.set(requestKey, abortController);

    try {
      const response = await fetch(
        `/next/gettimurank?biao=fbsy&id=${questionId}&allcateid=${allcateid}`,
        {
          signal: abortController.signal,
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // 构造排名字符串
      const rankStr = result.res
        .map((item) => `${item.name}(${item.cateid})-${item.rank}`)
        .join('-');

      // 更新排名显示
      rank.value = rankStr;
      if (data.value && data.value[itemIndex]) {
        data.value[itemIndex].rank = rankStr;
      }

      console.log('✅ 排名加载完成:', rankStr);
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('🚫 排名请求被取消（用户切换了题目）');
      } else {
        console.error('❌ 排名加载失败:', error);
        // 设置错误状态
        if (data.value && data.value[itemIndex]) {
          data.value[itemIndex].rank = '排名加载失败';
        }
      }
    } finally {
      // 清理请求记录
      pendingRankRequests.delete(requestKey);
    }
  };

  const prev_page = () => {
    left();
  };
  const next_page = () => {
    right();
  };

  const themeColor = ref('');
  watch(
    () => themeStore.theme,
    () => {
      nextTick(() => {
        themeColor.value = getComputedStyle(document.documentElement)
          .getPropertyValue('--theme-color')
          .trim();
      });
    },
    { immediate: true },
  );

  const getOptionStyle = (item, option) => {
    const highlightColor = '#1677EE'; // 正确答案高亮颜色
    const choiceColor = 'blue'; // 用户选择项颜色

    if (!isAnswerHighlighted.value) {
      return { color: themeColor.value };
    }

    // 高亮正确答案
    if (item.answer === option) {
      return { color: highlightColor };
    }

    // 如果用户已选择，且选择错误，高亮用户的选项
    if (item.choice && item.choice !== item.answer && item.choice === option) {
      return { color: choiceColor };
    }

    // 默认颜色
    return { color: themeColor.value };
  };

  // 更名为 toggleAnswerHighlight，并简化逻辑
  const toggleAnswerHighlight = () => {
    isAnswerHighlighted.value = !isAnswerHighlighted.value; // 切换高亮状态
  };

  // 简化 toggleansblock 函数，仅负责切换答案块的可见性
  const toggleansblock = async () => {
    daanblock.value = !daanblock.value;
  };
  // 防止重复触发的标志
  let isKeyProcessing = false;

  // 处理旧式按键事件（button.exe 可能使用这种方式）
  const handleLegacyKeyEvent = async (event) => {
    console.log('🔧 处理旧式按键事件:', {
      type: event.type,
      keyCode: event.keyCode,
      which: event.which,
      key: event.key,
      isTrusted: event.isTrusted,
    });

    // 根据 keyCode 判断按键（兼容旧的 button.exe）
    const keyCode = event.keyCode || event.which;

    // 只处理我们关心的按键，其他按键直接忽略
    if (keyCode === 37 || keyCode === 81) {
      // 37 = ArrowLeft, 81 = Q
      // 防止重复触发
      if (isKeyProcessing) {
        console.log('⚠️ 左键处理中，跳过重复事件');
        return;
      }
      console.log('⬅️ 旧式左键检测，执行 left()');
      event.preventDefault();
      isKeyProcessing = true;
      try {
        await left();
      } finally {
        setTimeout(() => {
          isKeyProcessing = false;
        }, 100);
      }
    } else if (keyCode === 39 || keyCode === 69) {
      // 39 = ArrowRight, 69 = E
      // 防止重复触发
      if (isKeyProcessing) {
        console.log('⚠️ 右键处理中，跳过重复事件');
        return;
      }
      console.log('➡️ 旧式右键检测，执行 right()');
      event.preventDefault();
      isKeyProcessing = true;
      try {
        await right();
      } finally {
        setTimeout(() => {
          isKeyProcessing = false;
        }, 100);
      }
    } else {
      // 忽略其他按键（如 NumLock），不设置处理标志
      console.log('🔇 忽略无关按键:', keyCode);
    }
  };

  const handleKeyDown = async (event) => {
    // 调试信息：记录所有按键事件
    console.log('🔍 按键事件调试:', {
      type: event.type,
      key: event.key,
      code: event.code,
      keyCode: event.keyCode,
      which: event.which,
      isTrusted: event.isTrusted,
      target: event.target.tagName,
      timestamp: Date.now(),
    });

    try {
      // 兼容 button.exe：检查 key 属性或 keyCode
      const isLeftKey =
        event.key === 'ArrowLeft' ||
        event.key === 'q' ||
        event.keyCode === 37 ||
        event.keyCode === 81;
      const isRightKey =
        event.key === 'ArrowRight' ||
        event.key === 'e' ||
        event.keyCode === 39 ||
        event.keyCode === 69;

      if (isLeftKey) {
        // 防止重复触发（只对左键检查）
        if (isKeyProcessing) {
          console.log('⚠️ 左键处理中，跳过重复事件');
          return;
        }
        console.log('⬅️ 检测到左键，执行 left()');
        event.preventDefault(); // 阻止默认行为
        isKeyProcessing = true;
        await left();
      } else if (isRightKey) {
        // 防止重复触发（只对右键检查）
        if (isKeyProcessing) {
          console.log('⚠️ 右键处理中，跳过重复事件');
          return;
        }
        console.log('➡️ 检测到右键，执行 right()');
        event.preventDefault(); // 阻止默认行为
        isKeyProcessing = true;
        await right();
      } else if (event.key === 'h') {
        const showAnsButton = document.querySelector('.an_a');
        await showAnsButton.click();
      } else if (event.key === 'j') {
        const showAnsButton = document.querySelector('.an_b');
        await showAnsButton.click();
      } else if (event.key === 'k') {
        const showAnsButton = document.querySelector('.an_c');
        await showAnsButton.click();
      } else if (event.key === 'l') {
        const showAnsButton = document.querySelector('.an_d');
        await showAnsButton.click();
      } else if (event.key === 'u') {
        await canvasStore.toggleCanvasVisibility(); // 调用 Canvas Store 的方法
      } else if (event.key === 'i') {
        // await canvasStore.initCanvas(); // 调用 Canvas Store 的方法，此行在 onMounted 中已经调用，无需重复
      } else if (event.key === 'e' && event.ctrlKey) {
        // Ctrl+E 备用擦除模式快捷键
        console.log('Ctrl+E pressed, toggling draw mode');
        event.preventDefault();
        event.stopPropagation();
        await canvasStore.toggleDrawMode(); // 调用 Canvas Store 的方法
      } else if (event.key === '[' && event.ctrlKey) {
        // Ctrl+[ 减小擦除范围
        event.preventDefault();
        canvasStore.updateEraseSize(Math.max(5, canvasStore.eraseSize - 5)); // 调用 Canvas Store 的方法
      } else if (event.key === ']' && event.ctrlKey) {
        // Ctrl+] 增大擦除范围
        event.preventDefault();
        canvasStore.updateEraseSize(Math.min(100, canvasStore.eraseSize + 5)); // 调用 Canvas Store 的方法
      } else if (event.key === 't' && event.ctrlKey) {
        // Ctrl+T 切换范围显示 (改为 T，避免与浏览器刷新冲突)
        event.preventDefault();
        canvasStore.toggleEraseIndicator(); // 调用 Canvas Store 的方法
      } else {
        // 忽略其他按键，不设置处理标志
        console.log('🔇 主函数忽略无关按键:', event.key, event.keyCode);
        return; // 直接返回，不进入 finally 块
      }
    } catch (error) {
      console.error('键盘事件处理错误:', error);
    } finally {
      // 只有处理了相关按键才重置标志
      setTimeout(() => {
        isKeyProcessing = false;
      }, 100); // 100ms 防抖延迟
    }
  };

  const updateValue = async (delta, force = false) => {
    open_count.value = 0;
    const max_page = Math.max(gendata.value.length, total.value);
    const newValue = Math.min(Math.max(1, +current_page.value + delta), max_page);

    if (newValue === +current_page.value && delta !== 0) {
      return; // 如果值未改变且不是初始加载，则不执行操作
    }

    // 在做题模式下，检查当前题目是否已提交成功（除非强制翻页）
    if (!force && exerciseStore.isZuotiMode && delta !== 0 && !currentQuestionSubmitted.value) {
      message.warning({
        content: '请先提交当前题目的答案再切换题目',
        style: {
          marginTop: '60vh',
        },
      });
      return; // 阻止切换题目
    }

    if (canvasStore.showCanvas) {
      await canvasStore.saveCanvasData(true);
    }

    current_page.value = newValue;
    // 切换题目时重置提交状态
    currentQuestionSubmitted.value = false;
    await getData();
  };

  const right = async (force = false) => {
    const kjid = route.query.kjid || null;
    if (kjid && +perpage.value !== 1) {
      let max_page = gendata.value.length > 0 ? gendata.value.length : total.value;

      maodianindex.value = maodianindex.value <= max_page ? maodianindex.value + 1 : max_page;
      let targetId = `id${maodianindex.value}`;
      const el = document.getElementById(targetId);
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else {
      await updateValue(1, force);
    }
  };

  const left = async (force = false) => {
    const kjid = route.query.kjid || null;
    if (kjid && +perpage.value !== 1) {
      maodianindex.value = +maodianindex.value > 0 ? maodianindex.value - 1 : 0;
      let targetId = `id${maodianindex.value}`;
      const el = document.getElementById(targetId);
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else {
      await updateValue(-1, force);
    }
  };

  // 恢复页面数据（recoverpage）
  const recoverpage = async () => {
    const url = '/next/fbrecoverpage';
    const id = route.query.id; // 原始id（可能是字符串，如'123'或其他值）
    const kjid = route.query.kjid; // kjid可能为undefined或字符串（如'1_3e_269vujp'）

    const type = route.query.type || current_type.value; // 使用 current_type.value 替代 currenttype.value

    // 处理cateid：当id为'123'时设为null，其他情况保留原始类型（不强制转数字）
    let cateid;
    if (id === '123') {
      // 注意：route.query.id是字符串，用字符串比较
      cateid = null;
    } else {
      cateid = id; // 保留原始值（可能是字符串或数字，后端会根据业务处理）
    }

    // 构造请求参数
    const params = {
      cateid: cateid,
      kjid: kjid, // 直接传递原始kjid（字符串或undefined）
      type: type,
    };

    console.log('recoverpage请求参数：', params);

    try {
      const response = await axios.get(url, { params });
      // 后端返回的page可能是数字，直接赋值
      current_page.value = response.data.page ?? current_page.value;
      console.log('恢复的页码：', current_page.value);
    } catch (error) {
      console.error('recoverpage请求失败：', error);
    }
  };

  // 保存页面数据（savepage）
  const savepage = async () => {
    // 如果存在ids参数，直接返回（保持你的原有逻辑）
    if (route.query.ids) {
      return;
    }

    const url = '/next/fbremeber';
    const type = route.query.type || current_type.value; // 使用 current_type.value 替代 currenttype.value
    const id = route.query.id; // 原始id（字符串）
    const kjid = route.query.kjid; // 原始kjid

    // 处理cateid：同recoverpage逻辑，保持一致性
    let cateid;
    if (id === '123') {
      // 字符串比较，匹配路由参数类型
      cateid = null;
    } else {
      cateid = id; // 保留原始值，不强制转数字
    }

    // 构造请求体
    const postData = {
      cateid: cateid,
      kjid: kjid, // 直接传递原始kjid
      type: type,
      page: current_page.value, // 页码是数字，直接传递
    };

    console.log('savepage请求数据：', postData);

    try {
      const response = await axios.post(url, postData);
      if (response.data.affectedRows) {
        console.log(`成功记住页码：${current_page.value}`);
      }
    } catch (error) {
      console.error('savepage请求失败：', error);
    }
  };
  const pushans = async (id, answer) => {
    const type = route.query.type || 'sy';
    console.log('📝 用户点击选项提交答案:', { id, answer, type });
    // 使用 exerciseStore 提交答案
    const submitResult = await exerciseStore.submitAnswer(
      id,
      answer,
      type,
      route.query.kjid,
      async () => {
        // 成功回调：用户主动提交答案成功
        console.log('✅ 用户答案提交成功，标记状态并自动翻页');
        currentQuestionSubmitted.value = true; // 标记当前题目已提交成功
        await right(); // 提交成功后自动翻页
      },
      (error) => {
        // 失败回调
        console.error('❌ 用户答案提交失败:', error);
      },
    );

    // 双重保险：确保状态更新
    if (submitResult) {
      currentQuestionSubmitted.value = true;
      console.log('✅ pushans: 状态已更新为已提交');
    }
  };

  const pushans1 = async (id, answer) => {
    const type = route.query.type || 'sy';
    console.log('🔄 自动提交答案（浏览记录）:', { id, answer, type });

    // pushans1 用于自动提交（记录浏览行为），不自动翻页
    const submitResult = await exerciseStore.submitAnswer(
      id,
      answer,
      type,
      route.query.kjid,
      () => {
        // 成功回调：自动提交成功
        console.log('✅ 自动提交成功（浏览记录）');
        // 这里也要更新状态，因为这代表题目已经有答案了
        currentQuestionSubmitted.value = true;
      },
      (error) => {
        // 失败回调
        console.error('❌ 自动提交失败:', error);
      },
    );

    if (submitResult) {
      console.log('✅ pushans1: 自动提交成功，状态已更新');
    }

    return submitResult;
  };

  const updateids = async () => {
    const url = `/next/updateidsnull`;
    const ids = updateidslist.value.join(',');
    console.log(`ids`, ids);
    let params = {
      ids: ids,
    };
    const response = await axios.get(url, { params });
    if (+response.data.data.changedRows !== 0) {
      message.success({
        content: response.data.data.message,
        style: {
          marginTop: '80vh',
        },
      });
    }

    await getData();
  };
  const getData = async () => {
    showvideo.value = false;
    // 只在第一次加载题目时显示动画
    if (firstLoad.value) {
      loadingStore.startLoading();
      firstLoad.value = false;
    }
    // 确保每次切换题目都回到顶部
    if (grid.value) {
      // 桌面端：滚动主窗口和所有 scroll-container
      window.scrollTo({ top: 0, behavior: 'smooth' });
      document.querySelectorAll('.scroll-container').forEach((el) => {
        el.scrollTo ? el.scrollTo({ top: 0, behavior: 'smooth' }) : (el.scrollTop = 0);
      });
    } else {
      // 移动端：主要滚动 .wp 容器，同时也滚动主窗口
      const wpContainer = document.querySelector('.wp');
      if (wpContainer) {
        wpContainer.scrollTo({ top: 0, behavior: 'smooth' });
      }
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    perpage.value = +perpage.value;
    biao.value = route.query.biao || 'fbsy';
    const per = route.query.per || perpage.value;
    const a = route.query.a || false;
    const id = route.query.id || false;
    const z = route.query.z || 0;
    const b = route.query.b || 0;
    const t = route.query.t || 0;
    const f = route.query.f || 0;
    const o = route.query.o || 0;
    const ids = route.query.ids;
    const q = route.query.q;
    const parentid = route.query.parentid;
    const zql = route.query.zql || 0;
    const biao1 = route.query.biao || 'fbsy';
    const page = route.query.page || current_page.value;
    const type = route.query.type || 'syzc';
    const kjid = route.query.kjid || null;
    current_type.value = route.query.type + '' || 'gwy';
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = `/next/fbtimu`;

    let params = {
      per: per,
      page: page,
      id: id,
      type: type,
      z: z,
      b: b,
      f: f,
      o: o,
      q: q,
      ids: ids,
      biao: biao1,
      t: !t ? 0 : t,
      zql: zql,
      kjid: kjid,
      parentid: parentid,
      fast: '1', // 🚀 启用快速模式，跳过不必要的数据库查询
    };
    console.log('params', params);
    try {
      if (kjid) {
        if (gendata.value.length === 0) {
          // 🚀 性能优化：使用快速模式只获取基本信息
          const fastParams = { ...params, fast: '1' };
          const response = await axios.get(`/next/fbgethtimu`, { params: fastParams });
          total.value = gendata.value.length || 0;
          console.log('total', total.value);
          gendata.value = response.data;

          response.data.map((item) => {
            kjids.value.push(item.globalId);
          });
          response.data.map((item) => {
            updateidslist.value.push(item.id);
          });
          console.log(`updateidslist.value`, updateidslist.value);
          console.log(`kjids`, kjids.value);
          params.ids = gendata.value[current_page.value - 1].id;
        } else {
          let ids = gendata.value.map((item) => item.id);
          idslist.value = ids.slice(0, perpage.value);
          console.log(`idslist`, idslist.value);
          // params.ids = gendata.value[current_page.value - 1].id;
          params.ids =
            +perpage.value === 1
              ? gendata.value[current_page.value - 1].id
              : idslist.value.join(',');
        }
      }

      function mapOrder(array, order, key) {
        const indexMap = order.reduce((m, id, idx) => {
          m[id] = idx;
          return m;
        }, {});
        return array.sort((a, b) => {
          const ia = indexMap[a[key]];
          const ib = indexMap[b[key]];
          // 若有 id 不在 order 中，可放在末尾
          return (ia !== undefined ? ia : Infinity) - (ib !== undefined ? ib : Infinity);
        });
      }

      const response = await axios.get(url, { params });

      await savepage();

      if (a) {
        // showContent.value = true; // 移除：showContent 已移除
      }
      // console.log(response.data.pagetotal[0].total);
      const sorted = mapOrder(response.data.data, idslist.value, 'id');
      data.value = sorted;

      console.log('data', data.value);
      if (+perpage.value === 1) {
        tid.value = data.value[0].id;
      }
      tid.value = data.value[0].id;

      // 🎯 设置当前题目ID到canvasStore（修复：应该在循环外设置）
      canvasStore.setCurrentQuestionId(data.value[0].id);

      for (let item in data.value) {
        data.value[item].content = data.value[item].content.replace(/fenbike/g, 'fbstatic');
        if (data.value[item].allcateid.includes('656598')) {
          // 定义需要替换的关键词列表
          const KEYWORDS = [
            '应',
            '更',
            '而',
            '但',
            '要',
            '需要',
            '然而',
            '所以',
            '因此',
            '但是',
            '不过',
          ];

          // 构建正则表达式，使用管道符连接所有关键词
          const KEYWORD_REGEX = new RegExp(KEYWORDS.join('|'), 'g');

          // 替换函数
          data.value[item].content = data.value[item].content.replace(KEYWORD_REGEX, (match) => {
            if (match === '因此') {
              return `<span style="color: var(--theme-accent)">${match}</span>`;
            }
            return `<span style="color: var(--theme-color)">${match}</span>`;
          });
        }

        if (!data.value[item].solution.match(/<p>A/g)) {
          data.value[item].solution = data.value[item].solution
            .replace(/A项/g, '<br/>A项')
            .replace(/fenbike/g, 'fbstatic');
        }
        if (!data.value[item].solution.match(/<p>B/g)) {
          data.value[item].solution = data.value[item].solution
            .replace(/B项/g, '<br/>B项')
            .replace(/fenbike/g, 'fbstatic');
        }
        if (!data.value[item].solution.match(/<p>C/g)) {
          data.value[item].solution = data.value[item].solution
            .replace(/C项/g, '<br/>C项')
            .replace(/fenbike/g, 'fbstatic');
        }
        if (!data.value[item].solution.match(/<p>D/g)) {
          data.value[item].solution = data.value[item].solution
            .replace(/D项/g, '<br/>D项')
            .replace(/fenbike/g, 'fbstatic');
        }
        data.value[item].answerone = data.value[item].answerone
          .replace(/<p>/g, '')
          .replace(/<\/p>/g, '')
          .replace(/fenbike/g, 'fbstatic');
        data.value[item].answertwo = data.value[item].answertwo
          .replace(/<p>/g, '')
          .replace(/<\/p>/g, '')
          .replace(/fenbike/g, 'fbstatic');
        data.value[item].answerthree = data.value[item].answerthree
          .replace(/<p>/g, '')
          .replace(/<\/p>/g, '')
          .replace(/fenbike/g, 'fbstatic');
        data.value[item].answerfour = data.value[item].answerfour
          .replace(/<p>/g, '')
          .replace(/<\/p>/g, '')
          .replace(/fenbike/g, 'fbstatic');
        // 🚀 异步排名加载：不阻塞主流程，在后台加载排名
        if ((biao1 === 'fbsy' && !alltimu.value) || kjid) {
          // 立即设置加载状态
          data.value[item].rank = '排名加载中...';

          // 异步加载排名，不等待结果
          loadRankAsync(data.value[item].id, data.value[item].allcateid, item);

          console.log('🚀 排名异步加载已启动，继续主流程');
        }
        if (exerciseStore.isZuotiMode) {
          // 使用 exerciseStore.isZuotiMode
          console.log(kjids.value, kjids.value[current_page.value - 1], data.value[item].answer);

          const submitResult = await pushans1(
            kjids.value[current_page.value - 1],
            data.value[item].answer,
          );
          if (submitResult) {
            currentQuestionSubmitted.value = true; // 标记当前题目已提交成功
          }
        }
      }

      if (+f === 1) {
        console.log('f');
        data.value = data.value.sort((a, b) => b.correctRatio - a.correctRatio);
      }
      if (+f === 2) {
        console.log('f');
        data.value = data.value.sort((a, b) => a.sort - b.sort);
      }
      if (+b === 1) {
        for (let item in data.value) {
          // console.log(data.value[item].answer);
          data.value[item].solution = '';
        }
      }
      total.value =
        gendata.value.length > 0 ? gendata.value.length : response.data.pagetotal[0].total;
      console.log('response.data', response.data);
      console.log('gendata.value.length', gendata.value.length);
      loadingStore.stopLoading(); // 使用 loadingStore.stopLoading()
      await nextTick();
      window.document.title = data.value[0].id;
      console.log(data.value[0].id);

      // 根据 anstype 设置新题目的答案高亮状态和答案块可见性
      if (anstype.value) {
        isAnswerHighlighted.value = true;
      } else {
        isAnswerHighlighted.value = false;
      }
      // showContent.value = true; // 确保答案块在加载新题目时可见 // 移除：showContent 已移除

      // 重新初始化画布并设置当前ID
      if (canvasStore.showCanvas) {
        // 使用 Canvas Store 的状态
        canvasStore.setCurrentQuestionId(data.value[0].id); // 🔥 修复：使用新题目的ID
        await canvasStore.initCanvas(); // 调用 Canvas Store 的方法
        // 加载新题目的canvas数据
        await canvasStore.loadCanvasData(); // 调用 Canvas Store 的方法
      }
    } catch (error) {
      console.error(error);
      loadingStore.stopLoading(); // 使用 loadingStore.stopLoading()
    }
  };

  const firstLoad = ref(true);

  // 🔧 简化：页面关闭时的处理（主要用于调试）
  const handleBeforeUnload = () => {
    console.log('🚪 sy 页面即将关闭，主题状态已自动保存');
  };

  onMounted(async () => {
    screenWidth.value = window.innerWidth;

    // This check is now handled by the ref initialization and resize listener
    // if (screenWidth.value <= 470) {
    //   grid.value = false;
    // }

    // ✅ 当宽度正好为 467 时，设置白色背景
    if (screenWidth.value > 460 && screenWidth.value < 470) {
      document.body.style.background = 'white';
    }
    document.body.style.overflow = 'visible';
    console.log('window.innerWidth', window.innerWidth);

    // 检测是否支持数位板笔 - 现在由 canvasStore 内部管理和同步
    // if ('PointerEvent' in window) {
    //   penSupported.value = true;
    //   console.log('支持指针事件，数位板笔可用');
    // } else {
    //   console.log('不支持指针事件，数位板笔不可用');
    // }

    // 添加键盘事件监听器（只添加一次）
    document.addEventListener('keydown', handleKeyDown);

    // 为了调试 button.exe，添加更多事件监听器
    document.addEventListener('keypress', (event) => {
      console.log('🔍 keypress 事件:', {
        type: event.type,
        key: event.key,
        code: event.code,
        keyCode: event.keyCode,
        which: event.which,
        isTrusted: event.isTrusted,
      });
      // 也尝试处理 keypress 事件
      handleLegacyKeyEvent(event);
    });

    document.addEventListener('keyup', (event) => {
      console.log('🔍 keyup 事件:', {
        type: event.type,
        key: event.key,
        code: event.code,
        keyCode: event.keyCode,
        which: event.which,
        isTrusted: event.isTrusted,
      });
      // 也尝试处理 keyup 事件
      handleLegacyKeyEvent(event);
    });

    // 也在 window 上监听，以防 button.exe 发送到 window 级别
    window.addEventListener('keydown', (event) => {
      console.log('🔍 window keydown 事件:', {
        type: event.type,
        key: event.key,
        code: event.code,
        keyCode: event.keyCode,
        which: event.which,
        isTrusted: event.isTrusted,
      });
    });

    await recoverpage();
    await getData();
    getResolution();

    // 🔥 移除自动初始化画布，画布应该只在用户点击时才初始化
    console.log('💡 页面加载完成，画布将在用户点击时初始化');

    // 🚀 启用画布实时传输功能
    try {
      await canvasStore.enableRealtimeTransmission();
      console.log('✅ 画布实时传输已启用');
    } catch (error) {
      console.error('❌ 启用画布实时传输失败:', error);
    }

    window.addEventListener('resize', getResolution);

    window.addEventListener('resize', () => {
      screenWidth.value = window.innerWidth;
      grid.value = window.innerWidth > 768; // Automatically toggle grid based on screen width
    });

    // 🔧 简化：sy 页面不需要手动初始化主题，App.vue 会自动处理
    // 添加页面关闭/刷新事件监听器
    window.addEventListener('beforeunload', handleBeforeUnload);
  });

  onBeforeUnmount(() => {
    // 🔧 简化：App.vue 会自动处理主题切换，这里只需要清理资源
    console.log('🚪 sy 页面组件即将卸载');

    // 清理canvasManager资源 - 现在由 canvasStore 内部管理
    canvasStore.cleanupCanvas(); // 调用 Canvas Store 的方法

    // 移除事件监听器
    document.removeEventListener('keydown', handleKeyDown);
    window.removeEventListener('resize', getResolution);
    window.removeEventListener('resize', canvasStore.initCanvas); // 调用 Canvas Store 的方法
    window.removeEventListener('beforeunload', handleBeforeUnload);
  });

  const showCanvasTip = ref(true); // 这个保留，因为它控制 canvas-tip 的显示
</script>
<template>
  <a-row class="main-row" style="height: 100vh">
    <!-- 左侧按钮区 -->
    <a-col :xl="2" :lg="24" :md="24" :sm="24" :xs="24">
      <span class="top-input" v-if="showTop">
        <!-- ...原按钮区内容... -->
        <a-input-number v-model:value="perpage" :min="1" @change="getData" />
        <a-button @click="prev_page">p</a-button>
        <a-input-number v-model:value="current_page" :min="1" :max="total" @change="getData" />
        <a-button @click="next_page">n</a-button>
        <a-button @click="getData">刷新</a-button>
        <a-input-number v-model:value="tid" disabled />
        <a-button :type="exerciseStore.isZuotiMode ? 'primary' : 'dashed'" @click="zuotimode"
          >做题模式{{ exerciseStore.isZuotiMode ? '开' : '关' }}</a-button
        >
        <el-dropdown>
          <el-button type="primary"> 下拉 </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="toggledaanblock">隐藏解析区</el-dropdown-item>
              <el-dropdown-item @click="showds = !showds">showds</el-dropdown-item>
              <el-dropdown-item @click="showbtn = !showbtn">隐藏按钮</el-dropdown-item>
              <el-dropdown-item @click="toggleAnswerHighlight">{{
                isAnswerHighlighted ? '隐藏答案' : '显示答案'
              }}</el-dropdown-item>
              <el-dropdown-item @click="showvideo = !showvideo">{{
                showvideo === true ? '隐藏视频' : '显示视频'
              }}</el-dropdown-item>
              <el-dropdown-item @click="anstype = !anstype">{{
                anstype === false ? '开答案' : '关答案'
              }}</el-dropdown-item>
              <el-dropdown-item @click="allshow">all</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <a-button @click="toggleTop">隐藏按钮</a-button>
        <a-button @click="themeStore.toggleBackground()">背景</a-button>
        <a-button style="background-color: aquamarine" @click="canvasStore.toggleCanvasVisibility()"
          >画布</a-button
        >
        <a-button
          v-show="canvasStore.showCanvas"
          :type="canvasStore.isEraseMode ? 'primary' : 'default'"
          @click="canvasStore.toggleDrawMode()"
          >{{ canvasStore.isEraseMode ? '擦除模式' : '绘制模式' }}</a-button
        >
        <a-button v-show="canvasStore.showCanvas" @click="canvasStore.initCanvas()"
          >init画布</a-button
        >
        <a-button v-show="canvasStore.showCanvas" @click="canvasStore.clearCanvas()"
          >清除画布</a-button
        >
        <a-button v-show="canvasStore.showCanvas" @click="canvasStore.saveCanvasData()"
          >保存画布</a-button
        >
        <a-button v-show="canvasStore.showCanvas" @click="canvasStore.loadCanvasData()"
          >加载画布</a-button
        >
        <a-input-number
          v-show="canvasStore.showCanvas"
          v-model:value="canvasStore.eraseSize"
          :min="5"
          :max="100"
          style="width: 80px"
          placeholder="擦除范围"
          @change="canvasStore.updateEraseSize"
        />
        <a-button
          v-show="canvasStore.showCanvas"
          :type="canvasStore.showEraseIndicator ? 'primary' : 'default'"
          @click="canvasStore.toggleEraseIndicator()"
          >{{ canvasStore.showEraseIndicator ? '隐藏范围' : '显示范围' }}</a-button
        >
        <!-- 🚀 实时传输状态显示 -->
        <a-button
          v-show="canvasStore.showCanvas"
          :type="canvasStore.socketConnected ? 'primary' : 'default'"
          :style="{
            backgroundColor: canvasStore.socketConnected ? '#52c41a' : '#ff4d4f',
            borderColor: canvasStore.socketConnected ? '#52c41a' : '#ff4d4f',
            color: 'white',
          }"
          @click="
            canvasStore.socketConnected
              ? canvasStore.disableRealtimeTransmission()
              : canvasStore.enableRealtimeTransmission()
          "
        >
          {{ canvasStore.socketConnected ? '🟢 实时传输' : '🔴 离线模式' }}
          <span v-if="canvasStore.activeUsers > 1">({{ canvasStore.activeUsers }}人)</span>
        </a-button>
        <a-button @click="grid = !grid">grid{{ screenWidth }}</a-button>
        <a-button @click="refreshPage">刷页</a-button>
        <a :href="`/fb/update?biao=${biao}&timuid=${tid}`" target="_blank">
          <a-button color="#626aef" style="background-color: #6f9080; border-color: #6f9080"
            >编辑</a-button
          >
        </a>
        <a-button style="background-color: #cc99ff; border-color: #cc99ff" @click="setnull"
          >snull</a-button
        >
        <a-popconfirm
          title="确定要全部重新更新？"
          ok-text="Yes"
          cancel-text="No"
          @confirm="updateids"
          @cancel="message.error('Click on No')"
        >
          <a-button>全null</a-button>
        </a-popconfirm>
        <a-popconfirm
          title="确定要全部提交？"
          ok-text="Yes"
          cancel-text="No"
          @confirm="submit"
          @cancel="message.error('Click on No')"
        >
          <a-button :type="exerciseStore.isZuotiMode ? 'primary' : 'dashed'">提交</a-button>
        </a-popconfirm>
        <a-button
          :style="{
            background:
              themeStore.syTheme === 'plain'
                ? '#ffffff'
                : themeStore.syTheme === 'day'
                  ? '#f8f5e6'
                  : '#222222',
            color:
              themeStore.syTheme === 'plain'
                ? '#000000'
                : themeStore.syTheme === 'day'
                  ? '#333333'
                  : '#e0e0e0',
          }"
          @click="themeStore.cycleSyTheme()"
        >
          <span v-if="themeStore.syTheme === 'plain'">⚪️ 纯白</span>
          <span v-else-if="themeStore.syTheme === 'day'">🌞 日间</span>
          <span v-else>🌙 夜间</span>
        </a-button>
        <div
          v-show="canvasStore.showCanvas"
          style="background: #faf5eb; position: fixed; top: 0; position: relative"
        >
          <div class="top-button"></div>
        </div>
      </span>
      <div class="lb" @click="left"></div>
    </a-col>
    <!-- 主内容区 -->
    <a-col
      :xl="20"
      :lg="24"
      :md="24"
      :sm="24"
      :xs="24"
      class="wp"
      style="height: 100vh; overflow-y: auto; position: relative"
    >
      <!-- Loading 动画 -->
      <div v-if="loadingStore.isLoading" class="fancy-loading">
        <div class="rainbow-spinner">
          <span
            v-for="n in 8"
            :key="n"
            :class="`rainbow rainbow${n}`"
            :style="{ transform: `rotate(${n * 45}deg)` }"
          ></span>
        </div>
        <div class="fancy-text">🚀 正在加载题目，请稍候...</div>
      </div>
      <template v-else>
        <canvas
          v-show="canvasStore.showCanvas"
          id="drawingCanvas"
          style="
            position: absolute;
            background-color: rgba(255, 255, 255, 0);
            touch-action: none;
            user-select: none;
          "
          @mousedown="canvasStore.startDrawing"
          @mouseup="canvasStore.stopDrawing"
          @mousemove="canvasStore.handleMouseMove"
          @mouseleave="canvasStore.handleMouseLeave"
          @contextmenu.prevent
          @touchstart="canvasStore.startDrawingTouch"
          @touchmove="canvasStore.drawTouch"
          @touchend="canvasStore.stopDrawing"
          @pointerdown="canvasStore.handlePointerDown"
          @pointermove="canvasStore.handlePointerMove"
          @pointerup="canvasStore.handlePointerUp"
          @pointerleave="canvasStore.handlePointerLeave"
          @touchstart.passive="false"
          @touchmove.passive="false"
          @touchend.passive="false"
        ></canvas>
        <canvas
          v-show="canvasStore.showCanvas"
          id="indicatorCanvas"
          style="
            position: absolute;
            background-color: transparent;
            pointer-events: none;
            user-select: none;
          "
          @mousemove="canvasStore.updateMousePosition"
        ></canvas>
        <div v-if="canvasStore.showCanvas && showCanvasTip" class="canvas-tip">
          <button class="canvas-tip-close" @click="showCanvasTip = false">×</button>
          鼠标左键绘制 | 数位板笔{{ canvasStore.penSupported ? '✓' : '✗' }} | 点击按钮切换模式 |
          Ctrl+C/Ctrl+E 擦除模式 | Ctrl+[ 缩小擦除 | Ctrl+] 扩大擦除 | Ctrl+T 显示范围
          <br />擦除范围: {{ canvasStore.eraseSize }} | 范围显示:
          {{ canvasStore.showEraseIndicator ? '开' : '关' }} | 当前模式:
          {{ canvasStore.isEraseMode ? '擦除' : '绘制'
          }}<br v-if="canvasStore.penPressure > 0" />笔压力:
          {{ (canvasStore.penPressure * 100).toFixed(1) }}% | 倾斜: X{{ canvasStore.penTiltX }}° Y{{
            canvasStore.penTiltY
          }}°
        </div>
        <a-row v-for="(item, index) in data" :key="index" class="datalist">
          <a-col v-if="grid" :id="`id` + index" :span="12" @click="maodianindex = index">
            <div
              class="scroll-container"
              style="
                max-height: 98vh;
                overflow-y: auto;
                overflow-x: visible;
                border-top: 3px solid black;
              "
            >
              <!-- ...题目内容、选项、答案区...（与原来一致）... -->
              <div v-if="loadingStore.isLoading">Loading...</div>
              <div v-if="!loadingStore.isLoading" :class="index === 1 ? 'items' : ''">
                <div v-if="item.material" class="maintimu" v-html="item.material"></div>
                <div class="maintimu" style="" v-html="item.content"></div>
                <div class="maintimu">
                  <p
                    class="an_a"
                    :style="getOptionStyle(item, 'A')"
                    @click="pushans(kjids[current_page - 1], 'A')"
                    v-html="item.answerone"
                  ></p>
                  <p
                    class="an_b"
                    :style="getOptionStyle(item, 'B')"
                    @click="pushans(kjids[current_page - 1], 'B')"
                    v-html="item.answertwo"
                  ></p>
                  <p
                    class="an_c"
                    :style="getOptionStyle(item, 'C')"
                    @click="pushans(kjids[current_page - 1], 'C')"
                    v-html="item.answerthree"
                  ></p>
                  <p
                    class="an_d"
                    :style="getOptionStyle(item, 'D')"
                    @click="pushans(kjids[current_page - 1], 'D')"
                    v-html="item.answerfour"
                  ></p>
                </div>
                <dp v-if="zuotivideo" url=" http://127.0.0.1:9001/1.mp4"></dp>
                <p @click="toggleTop">----------------------</p>
                <div v-show="daanblock" class="answer">
                  <div @click="open_comment(item.id)">
                    <span>{{ item.choice }}</span
                    >{{ item.source }}{{ item.createdTime
                    }}<span
                      >{{ '正确率：' + Math.round(item.correctRatio) }}{{ '易错项：'
                      }}{{
                        +item.mostWrongAnswer === 0
                          ? 'A'
                          : +item.mostWrongAnswer === 1
                            ? 'B'
                            : +item.mostWrongAnswer === 2
                              ? 'C'
                              : +item.mostWrongAnswer === 3
                                ? 'D'
                                : item.mostWrongAnswer
                      }}</span
                    >
                    <p style="color: var(--theme-accent)">
                      {{ '排序：' + item.rank }}{{ item.extra ? '有extra' : '无extra' }}
                    </p>
                  </div>
                  <div>
                    <comment
                      v-if="showcomment && (+perpage === 1 || kjid)"
                      :uid="+item.id"
                      :type="current_type === 'gwy' ? 48644 : 6"
                    ></comment>
                  </div>
                  <div>
                    <div :style="showdaanblock" v-html="item.solution"></div>
                  </div>
                  <div>
                    <dp1 v-if="showvideo" :url="item.id" :uid="item.id"></dp1>
                  </div>
                  <p @click="toggleTop">================</p>
                </div>
              </div>
            </div>
          </a-col>
          <a-col v-if="!grid" :id="`id` + index" :span="24" @click="maodianindex = index">
            <div>
              <!-- ...同上，内容合并... -->
              <div v-if="loadingStore.isLoading">Loading...</div>
              <div v-if="!loadingStore.isLoading" :class="index === 1 ? 'items' : ''">
                <div v-if="item.material" class="maintimu" v-html="item.material"></div>
                <div class="maintimu" style="" v-html="item.content"></div>
                <div class="maintimu">
                  <p
                    class="an_a"
                    :style="getOptionStyle(item, 'A')"
                    @click="pushans(kjids[current_page - 1], 'A')"
                    v-html="item.answerone"
                  ></p>
                  <p
                    class="an_b"
                    :style="getOptionStyle(item, 'B')"
                    @click="pushans(kjids[current_page - 1], 'B')"
                    v-html="item.answertwo"
                  ></p>
                  <p
                    class="an_c"
                    :style="getOptionStyle(item, 'C')"
                    @click="pushans(kjids[current_page - 1], 'C')"
                    v-html="item.answerthree"
                  ></p>
                  <p
                    class="an_d"
                    :style="getOptionStyle(item, 'D')"
                    @click="pushans(kjids[current_page - 1], 'D')"
                    v-html="item.answerfour"
                  ></p>
                </div>
                <dp v-if="zuotivideo" url=" http://127.0.0.1:9001/1.mp4"></dp>
                <div v-if="item.ds" v-show="showds" class="ds">
                  <p @click="toggleTop">--------------------------</p>
                  <span
                    >{{ item.source }}{{ item.createdTime
                    }}{{ '正确率：' + Math.round(item.correctRatio) }}{{ '易错项：'
                    }}{{
                      +item.mostWrongAnswer === 0
                        ? 'A'
                        : +item.mostWrongAnswer === 1
                          ? 'B'
                          : +item.mostWrongAnswer === 2
                            ? 'C'
                            : +item.mostWrongAnswer === 3
                              ? 'D'
                              : item.mostWrongAnswer
                    }}</span
                  >
                  <p style="color: var(--theme-accent)">
                    {{ '排序：' + item.rank }}{{ item.extra ? '有extra' : '无extra' }}
                  </p>
                  <Shuxue :content="item.ds" />
                  <p @click="toggleTop">================</p>
                </div>
                <div v-show="daanblock" class="answer">
                  <div @click="open_comment(item.id)">
                    <span>{{ item.choice }}</span
                    >{{ item.source }}{{ item.createdTime
                    }}<span
                      >{{ '正确率：' + Math.round(item.correctRatio) }}{{ '易错项：'
                      }}{{
                        +item.mostWrongAnswer === 0
                          ? 'A'
                          : +item.mostWrongAnswer === 1
                            ? 'B'
                            : +item.mostWrongAnswer === 2
                              ? 'C'
                              : +item.mostWrongAnswer === 3
                                ? 'D'
                                : item.mostWrongAnswer
                      }}</span
                    >
                  </div>
                  <div>
                    <div :style="showdaanblock" v-html="item.solution"></div>
                  </div>
                  <div>
                    <dp1 v-if="showvideo" :url="item.id" :uid="item.id"></dp1>
                  </div>
                  <div>
                    <comment
                      v-if="showcomment && (+perpage === 1 || kjid)"
                      :uid="+item.id"
                      :type="current_type === 'gwy' ? 48644 : 6"
                    ></comment>
                  </div>
                </div>
                <p @click="toggleTop">================</p>
              </div>
            </div>
          </a-col>
          <a-col
            v-if="grid"
            :span="12"
            style="border-left: 2px solid black; border-top: 3px solid black"
            @click="maodianindex = index"
          >
            <div
              class="scroll-container"
              style="max-height: 98vh; overflow-y: auto; overflow-x: visible; scrollbar-width: none"
            >
              <div v-if="item.ds" v-show="showds" class="ds">
                <Shuxue :content="item.ds" />
                <p @click="toggleTop">================</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </template>
    </a-col>
    <!-- 右侧按钮区（桌面显示，移动端隐藏） -->
    <a-col :xl="2" :lg="0" :md="0" :sm="0" :xs="0">
      <div v-show="showTop"><span class="top-bottom"></span></div>
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
  <!-- 移动端底部按钮 -->
  <div v-if="screenWidth <= 1199" class="fixed-buttons">
    <div class="btn-group left">
      <button class="btn" @click="left(true)">Left</button
      ><button class="btn" @click="right(true)">Right</button>
    </div>
    <div class="btn-group right">
      <button class="btn" @click="left(true)">Left</button
      ><button class="btn" @click="right(true)">Right</button>
    </div>
    <button type="button" class="top_button" @click="scrollToTop">top</button>
  </div>
</template>

<style scoped>
  /* sy.vue 专用样式 - 只保留组件特定的样式 */

  /* sy.vue中的容器特殊处理 - 允许水平滚动以支持表格 */
  .desktop-content {
    overflow-x: visible !important;
  }

  .desktop-content.no-scroll .wp,
  .mobile-content.no-scroll .wp {
    overflow-x: visible !important;
  }

  /* sy.vue 特有的样式覆盖 - 仅保留必要的组件特定样式 */

  /* sy.vue 特有的表格样式覆盖 */
  .markdown-content .table-container.scroll-mode {
    cursor: grab !important; /* 显示可拖拽的光标 */
    /* 🔧 修复：确保表格内容不够宽时不显示空白 */
    display: flex !important;
    flex-direction: column !important;
  }

  /* 🔧 滚动模式下的表格特殊处理 - sy.vue 特有 */
  .markdown-content .table-container.scroll-mode table {
    min-width: 100% !important; /* 确保表格至少占满容器宽度 */
    width: max-content !important; /* 根据内容自动调整宽度 */
  }

  /* 🔧 不需要滚动的表格特殊处理 - 消除右侧空白 - sy.vue 特有 */
  .markdown-content .table-container.scroll-mode.no-scroll-needed {
    overflow-x: visible !important; /* 不显示水平滚动条 */
    display: block !important; /* 恢复正常块级显示 */
  }

  .markdown-content .table-container.scroll-mode.no-scroll-needed table {
    width: 100% !important; /* 表格占满容器宽度 */
    min-width: auto !important; /* 移除最小宽度限制 */
  }

  /* 拖拽时的光标 */
  .markdown-content .table-container.scroll-mode:active {
    cursor: grabbing !important;
  }

  /* 数学公式在表格中的特殊处理 */
  .markdown-content .table-container .mjx-container {
    overflow-x: visible !important;
    overflow-y: visible !important;
    max-width: 100% !important; /* 修改：限制最大宽度为100% */
    white-space: normal !important; /* 修改：允许正常换行 */
    display: inline-block !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .markdown-content .table-container.scroll-mode .mjx-container {
    min-width: auto !important;
    width: auto !important;
  }

  /* 表格控制按钮样式 */
  .table-controls {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 0.5rem !important;
    padding: 0 0.5rem !important;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 1;
    }
  }

  .table-toggle-btn {
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
    background-color: #dbeafe !important;
    color: #1d4ed8 !important;
    border: none !important;
    border-radius: 0.375rem !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-family: 'LXGW WenKai', serif !important;
  }

  .table-toggle-btn:hover {
    background-color: #bfdbfe !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .table-toggle-btn svg {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }

  /* sy.vue 特有的画布样式覆盖 */
  canvas {
    border: 1px solid black;
    /* 优化画布渲染质量 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    /* 启用硬件加速 */
    transform: translateZ(0);
    will-change: transform;
    /* 优化文本渲染 */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #drawingCanvas {
    cursor: crosshair; /* 鼠标指针样式 */
    /* 额外的抗锯齿优化 */
    image-rendering: auto;
    image-rendering: smooth;
    image-rendering: high-quality;
    /* 确保平滑缩放 */
    -ms-interpolation-mode: bicubic;
  }

  /* 画布激活时的样式 */
  #drawingCanvas:active {
    cursor: crosshair;
  }

  #indicatorCanvas {
    /* 指示器画布也应用相同的优化 */
    image-rendering: auto;
    image-rendering: smooth;
    image-rendering: high-quality;
    transform: translateZ(0);
  }

  /* sy.vue 特有的响应式样式覆盖 */
  @media screen and (min-width: 1200px) {
    .desktop-content {
      overflow-x: visible !important; /* sy.vue中允许水平滚动 */
    }
  }

  @media screen and (max-width: 1199px) {
    .mobile-content .wp {
      overflow-x: visible !important; /* sy.vue中允许水平滚动 */
    }
  }
</style>
