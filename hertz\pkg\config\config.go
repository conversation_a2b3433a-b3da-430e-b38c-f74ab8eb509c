package config

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

var (
	// globalConfig 全局配置实例
	globalConfig *Config
)

// LoadConfig 加载配置文件
func LoadConfig(env ...string) (*Config, error) {
	var environment string

	// 如果传入了环境参数，使用传入的参数
	if len(env) > 0 && env[0] != "" {
		environment = env[0]
	} else {
		// 否则使用命令行参数或环境变量
		flag.StringVar(&environment, "env", "", "环境(dev或prod)，优先于环境变量HERTZ_ENV")
		if !flag.Parsed() {
			flag.Parse()
		}

		if environment == "" {
			environment = os.Getenv("HERTZ_ENV")
		}
		if environment == "" {
			environment = "dev"
		}
	}

	// 构建配置文件路径
	configFile := getConfigFilePath(environment)

	// 读取配置文件
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败 [%s]: %w", configFile, err)
	}

	// 解析YAML配置
	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("解析配置文件失败 [%s]: %w", configFile, err)
	}

	// 设置默认值
	setDefaultValues(&cfg)

	// 验证配置
	if err := validateConfig(&cfg); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 保存全局配置
	globalConfig = &cfg

	return &cfg, nil
}

// GetConfig 获取全局配置实例
func GetConfig() *Config {
	if globalConfig == nil {
		panic("配置未初始化，请先调用LoadConfig")
	}
	return globalConfig
}

// getConfigFilePath 获取配置文件路径
func getConfigFilePath(env string) string {
	var configFile string
	if env == "prod" {
		configFile = "config.prod.yaml"
	} else {
		configFile = "config.dev.yaml"
	}

	// 尝试多个可能的路径
	possiblePaths := []string{
		filepath.Join("config", configFile),       // ./config/config.dev.yaml
		filepath.Join("..", "config", configFile), // ../config/config.dev.yaml
		configFile, // ./config.dev.yaml
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	// 如果都找不到，返回默认路径
	return filepath.Join("config", configFile)
}

// setDefaultValues 设置默认配置值
func setDefaultValues(cfg *Config) {
	// 服务器默认配置
	if cfg.Server.Host == "" {
		cfg.Server.Host = "127.0.0.1"
	}
	if cfg.Server.Port == 0 {
		cfg.Server.Port = 8080
	}
	if cfg.Server.ReadTimeout == 0 {
		cfg.Server.ReadTimeout = 30
	}
	if cfg.Server.WriteTimeout == 0 {
		cfg.Server.WriteTimeout = 30
	}
	if cfg.Server.IdleTimeout == 0 {
		cfg.Server.IdleTimeout = 60
	}

	// 日志默认配置
	if cfg.Log.Level == "" {
		cfg.Log.Level = "info"
	}
	if cfg.Log.Format == "" {
		cfg.Log.Format = "json"
	}
	if cfg.Log.Output == "" {
		cfg.Log.Output = "stdout"
	}
	if cfg.Log.MaxSize == 0 {
		cfg.Log.MaxSize = 100
	}
	if cfg.Log.MaxBackups == 0 {
		cfg.Log.MaxBackups = 3
	}
	if cfg.Log.MaxAge == 0 {
		cfg.Log.MaxAge = 7
	}
}

// validateConfig 验证配置
func validateConfig(cfg *Config) error {
	// 验证MySQL配置
	if cfg.MySQL.Host == "" {
		return fmt.Errorf("MySQL host不能为空")
	}
	if cfg.MySQL.Port <= 0 || cfg.MySQL.Port > 65535 {
		return fmt.Errorf("MySQL port必须在1-65535之间")
	}
	if cfg.MySQL.User == "" {
		return fmt.Errorf("MySQL user不能为空")
	}
	if cfg.MySQL.DB == "" {
		return fmt.Errorf("MySQL database不能为空")
	}

	// 验证Redis配置
	if cfg.Redis.Host == "" {
		return fmt.Errorf("Redis host不能为空")
	}
	if cfg.Redis.Port <= 0 || cfg.Redis.Port > 65535 {
		return fmt.Errorf("Redis port必须在1-65535之间")
	}
	if cfg.Redis.DB < 0 || cfg.Redis.DB > 15 {
		return fmt.Errorf("Redis DB必须在0-15之间")
	}

	// 验证服务器配置
	if cfg.Server.Port <= 0 || cfg.Server.Port > 65535 {
		return fmt.Errorf("Server port必须在1-65535之间")
	}

	return nil
}

// ReloadConfig 重新加载配置（热重载）
func ReloadConfig(env ...string) error {
	newConfig, err := LoadConfig(env...)
	if err != nil {
		return err
	}

	globalConfig = newConfig
	return nil
}
