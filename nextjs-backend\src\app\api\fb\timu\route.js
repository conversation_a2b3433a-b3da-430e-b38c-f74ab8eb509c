/**
 * 粉笔题目获取API (简化版)
 * 对应原egg项目的 /egg/fbtimu 路由
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    
    // 解析参数
    const per = parseInt(searchParams.get('per')) || 10;
    const page = parseInt(searchParams.get('page')) || 1;
    const id = searchParams.get('id');
    const type = searchParams.get('type') || 'syzc';
    const z = searchParams.get('z');
    const b = searchParams.get('b');
    const f = searchParams.get('f');
    const o = searchParams.get('o');
    const q = searchParams.get('q');
    const ids = searchParams.get('ids');
    const biao = searchParams.get('biao') || 'fbsy';
    const t = searchParams.get('t');
    const zql = searchParams.get('zql');
    const kjid = searchParams.get('kjid');
    const parentid = searchParams.get('parentid');
    const fast = searchParams.get('fast');
    
    console.log('📚 timu API - 参数:', {
      per, page, id, type, biao, kjid, fast, ids
    });
    
    // 如果有具体的ids参数，优先处理
    if (ids) {
      const idList = ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      if (idList.length > 0) {
        console.log('🔍 查询指定IDs:', idList);
        
        const sql = `SELECT * FROM ${biao} WHERE id IN (${idList.map(() => '?').join(',')}) ORDER BY id ASC`;
        const data = await app.mysql.query(sql, idList);
        
        console.log('✅ 找到题目数量:', data.length);
        
        // 直接返回数组，与egg项目格式保持一致
        return NextResponse.json(data);
      }
    }
    
    // 如果有单个id参数
    if (id) {
      console.log('🔍 查询单个ID:', id);
      
      const timu = await app.mysql.get(biao, { id: parseInt(id) });
      
      if (timu) {
        console.log('✅ 找到题目:', timu.id);
        // 直接返回数组，与egg项目格式保持一致
        return NextResponse.json([timu]);
      } else {
        console.log('❌ 题目不存在:', id);
        // 直接返回空数组，与egg项目格式保持一致
        return NextResponse.json([]);
      }
    }
    
    // 构建查询条件
    const where = {};
    const options = {
      where,
      limit: per,
      offset: (page - 1) * per,
      orders: [['id', 'ASC']]
    };
    
    // 根据参数构建查询条件
    if (parentid) {
      where.parentid = parentid;
    }
    
    if (zql && zql !== '0') {
      where.correctRatio = zql;
    }
    
    if (q) {
      // 模糊查询内容
      const sql = `SELECT * FROM ${biao} WHERE content LIKE ? OR solution LIKE ? ORDER BY id ASC LIMIT ? OFFSET ?`;
      const searchTerm = `%${q}%`;
      const data = await app.mysql.query(sql, [searchTerm, searchTerm, per, (page - 1) * per]);
      
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM ${biao} WHERE content LIKE ? OR solution LIKE ?`;
      const countResult = await app.mysql.query(countSql, [searchTerm, searchTerm]);
      const total = countResult[0].total;
      
      // 直接返回数组，与egg项目格式保持一致
      return NextResponse.json(data);
    }
    
    // 执行查询
    const data = await app.mysql.select(biao, options);
    
    // 获取总数
    const whereClause = Object.keys(where).length > 0 ? 
      ' WHERE ' + Object.keys(where).map(key => `${key} = ?`).join(' AND ') : '';
    const countSql = `SELECT COUNT(*) as total FROM ${biao}${whereClause}`;
    const countResult = await app.mysql.query(countSql, Object.values(where));
    const total = countResult[0].total;
    
    console.log('✅ 题目数据获取成功:', {
      count: data.length,
      total,
      page,
      per
    });
    
    // 直接返回数组，与egg项目格式保持一致
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('❌ timu API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString(),
      params: Object.fromEntries(new URL(request.url).searchParams)
    }, { status: 500 });
  }
}

module.exports = { GET };
