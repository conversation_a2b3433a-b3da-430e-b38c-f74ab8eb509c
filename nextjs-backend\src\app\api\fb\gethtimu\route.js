/**
 * 粉笔题目获取API
 * 迁移自egg项目的fb.js gethtimu方法
 */

import { NextResponse } from 'next/server';
import { app } from '@/lib/app';
import axios from 'axios';

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

/**
 * 获取所有分类信息的辅助函数
 * 迁移自egg项目的getAllCategoryInfo方法
 */
async function getAllCategoryInfo(table, id) {
  try {
    const result = await app.mysql.get(table, { id });
    if (!result) return {};
    
    const categoryInfo = { [result.id]: result.name };
    
    if (result.parentid && result.parentid !== 0) {
      const parentInfo = await getAllCategoryInfo(table, result.parentid);
      Object.assign(categoryInfo, parentInfo);
    }
    
    return categoryInfo;
  } catch (error) {
    console.error('获取分类信息失败:', error);
    return {};
  }
}

/**
 * 格式化日期
 */
function dateformat(timestamp) {
  const date = new Date(timestamp);
  return date.toISOString().slice(0, 19).replace('T', ' ');
}

export async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const kjid = searchParams.get('kjid');
    const biao = searchParams.get('biao') || 'fbsy';
    const type = searchParams.get('type') || 'syzc';
    const fastMode = searchParams.get('fast') === '1' || searchParams.get('fast') === 'true';
    
    if (!kjid) {
      return NextResponse.json({ error: '缺少kjid参数' }, { status: 400 });
    }
    
    // 获取cookie
    const cookie = await app.redis.get('fbcookie');
    if (!cookie) {
      return NextResponse.json({ error: '未找到fbcookie' }, { status: 401 });
    }
    
    const headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
    };
    
    // 第一步：获取Exercise数据
    const exerciseResponse = await axios.get('https://tiku.fenbi.com/combine/exercise/getExercise', {
      params: {
        format: 'html',
        key: kjid,
        routecs: 'syzc',
        kav: 120,
        av: 120,
        hav: 120,
        app: 'web',
      },
      headers,
      withCredentials: true,
    });
    
    let qs, solutionResponse, flag, rkey;
    
    if (+exerciseResponse.data.code === 1) {
      flag = true;
      qs = 'exercise';
      rkey = exerciseResponse?.data?.data?.switchVO?.requestKey;
      console.log('🎯 使用exercise模式, requestKey:', rkey);
    } else {
      flag = false;
      qs = 'solution';
      console.log('🎯 需要获取solution数据...');
      
      // 获取Solution数据
      solutionResponse = await axios.get('https://tiku.fenbi.com/combine/exercise/getSolution', {
        params: {
          format: 'html',
          key: kjid,
          routecs: 'syzc',
          kav: 120,
          av: 120,
          hav: 120,
          app: 'web',
        },
        headers,
        withCredentials: true,
      });
      
      rkey = solutionResponse?.data?.data?.switchVO?.requestKey;
      console.log('🎯 使用solution模式, requestKey:', rkey);
    }
    
    if (!rkey) {
      console.log('🚨 未找到requestKey');
      return NextResponse.json([]);
    }
    
    // 检查消息状态
    const msg = flag ? exerciseResponse.data.msg : solutionResponse.data.msg;
    console.log('📋 API消息状态:', msg);
    
    if (msg !== 'SUCCESS') {
      console.log('🚨 API返回状态不是SUCCESS:', msg);
      return NextResponse.json([]);
    }
    
    // 快速模式：只返回题目ID列表
    if (fastMode) {
      console.log('🚀 gethtimu快速模式：获取完整题目列表');
      
      try {
        const staticResponse = await axios.get(`https://tiku.fenbi.com/combine/static/${qs}`, {
          params: {
            key: rkey,
            routecs: 'syzc',
            type: 1,
            kav: 120,
            av: 120,
            hav: 120,
            app: 'web',
          },
          headers,
          withCredentials: true,
        });
        
        const m = flag ? 'questions' : 'solutions';
        const questions = staticResponse.data[m] || [];
        console.log(`🔍 获取到完整${m}列表:`, questions.length, '个题目');
        
        const fastResult = questions.map((item) => ({
          id: item.id,
          globalId: item.globalId,
        }));
        
        return NextResponse.json(fastResult);
      } catch (error) {
        console.error('🚨 快速模式API调用失败:', error.response?.data || error.message);
        // 如果快速模式失败，继续执行正常流程
      }
    }
    
    // 正常模式：获取完整题目数据
    const staticResponse = await axios.get(`https://tiku.fenbi.com/combine/static/${qs}`, {
      params: {
        key: rkey,
        routecs: 'syzc',
        type: 1,
        kav: 120,
        av: 120,
        hav: 120,
        app: 'web',
      },
      headers,
      withCredentials: true,
    });
    
    const m = flag ? 'questions' : 'solutions';
    const questions = staticResponse.data[m];
    const result = [];
    const ids = questions.map(item => item.id).join(',');
    
    // 获取关键点信息
    const keypointsResponse = await axios.get(
      `https://tiku.fenbi.com/api/${type}/solution/keypoints?ids=${ids}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers }
    );
    
    let i = 0;
    
    for (const item of questions) {
      const globalId = item.globalId;
      
      // 查询数据库中是否已存在该题目
      const existingData = await app.mysql.get(biao, { id: item.id });
      
      if (existingData) {
        // 如果题目已存在，更新用户答案
        const userAnswers = solutionResponse?.data?.data?.userAnswers;
        const match = userAnswers?.[globalId];
        if (match) {
          existingData.choice = match?.answer?.choice;
        }
        
        // 处理ds字段中的```符号
        if (existingData?.ds && existingData.ds.match(/```/g)) {
          await app.mysql.update(biao, 
            { ds: existingData.ds.replace(/```/g, '') }, 
            { where: { id: existingData.id } }
          );
        }
        
        // 查找匹配的卡片和材料
        let matchedCard = null;
        for (const child of staticResponse.data.card.children) {
          for (const subChild of child.children) {
            if (subChild.key === item.globalId) {
              matchedCard = subChild;
              break;
            }
          }
          if (matchedCard) break;
        }
        
        // 查找匹配的材料
        const materialKey = matchedCard?.materialKeys?.[0];
        const matchedMaterial = materialKey
          ? staticResponse.data?.materials?.find((m) => m?.globalId === materialKey)
          : null;
        
        // 更新材料信息
        if (matchedMaterial?.content) {
          const updateObj = {
            material: matchedMaterial.content.replace(/fenbike/g, 'fbstatic'),
            materialKeys: matchedMaterial.globalId,
            parentid: matchedMaterial.id,
          };
          await app.mysql.update(biao, updateObj, { where: { id: existingData.id } });
        }
        
        existingData.globalId = item.globalId;
        result.push(existingData);
      } else if (!flag) {
        // 如果题目不存在且是solution模式，创建新题目
        const obj = { id: item.id };
        
        // 查找匹配的卡片和材料
        let matchedCard = null;
        for (const child of staticResponse.data.card.children) {
          for (const subChild of child.children) {
            if (subChild.key === item.globalId) {
              matchedCard = subChild;
              break;
            }
          }
          if (matchedCard) break;
        }
        
        const materialKey = matchedCard?.materialKeys?.[0];
        const matchedMaterial = materialKey
          ? staticResponse.data.materials?.find((m) => m.globalId === materialKey)
          : null;
        
        obj.material = matchedMaterial?.content?.replace(/fenbike/g, 'fbstatic') || '';
        obj.materialKeys = matchedMaterial?.globalId || '';
        obj.parentid = matchedMaterial?.id || null;
        obj.content = item.content?.replace(/fenbike/g, 'fbstatic') || '';
        
        // 处理选项
        if (item.accessories?.[0]?.options) {
          const options = item.accessories[0].options;
          obj.answerone = options[0] || '';
          obj.answertwo = options[1] || '';
          obj.answerthree = options[2] || '';
          obj.answerfour = options[3] || '';
        }
        
        // 处理用户答案
        const userAnswers = solutionResponse?.data?.data?.userAnswers;
        const match = userAnswers?.[globalId];
        if (match) {
          obj.choice = match.answer.choice;
        }
        
        // 处理正确答案
        const choiceMap = { 0: 'A', 1: 'B', 2: 'C', 3: 'D' };
        obj.answer = choiceMap[+item.correctAnswer.choice] || 'D';
        
        obj.solution = item.solution?.replace(/fenbike/g, 'fbstatic') || '';
        obj.source = item.source || '';
        obj.createdTime = dateformat(item.createdTime || Date.now());
        obj.ds = '重新更新';
        obj.allcateid = '';
        obj.tag = null;
        obj.correctRatio = null;
        obj.mostWrongAnswer = '';
        
        // 处理分类信息
        const textArray = [];
        if (keypointsResponse.data[i]) {
          for (const itemx in keypointsResponse.data[i]) {
            const all = await getAllCategoryInfo('fbsycate', keypointsResponse.data[i][itemx].id);
            const result = Object.values(all).join(',');
            textArray.push(result);
          }
        }
        
        const text = textArray.join(',');
        const newTextArray = text.split(',');
        const uniqueTextArray = [...new Set(newTextArray)];
        obj.allcateid = uniqueTextArray.join(',');
        
        // 检查是否已存在，如果不存在则创建
        const existingCheck = await app.mysql.get(biao, { id: item.id });
        if (!existingCheck) {
          await app.mysql.insert(biao, obj);
        } else {
          const updateObj = {
            choice: obj.choice,
            material: obj.material,
            materialKeys: obj.materialKeys,
            parentid: obj.parentid,
          };
          await app.mysql.update(biao, updateObj, { where: { id: obj.id } });
        }
        
        i++;
      }
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('❌ gethtimu API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
