/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/routes/route";
exports.ids = ["app/api/routes/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Froutes%2Froute&page=%2Fapi%2Froutes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froutes%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Froutes%2Froute&page=%2Fapi%2Froutes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froutes%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var I_workspace_nextjs_backend_src_app_api_routes_route_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/routes/route.js */ \"(rsc)/./src/app/api/routes/route.js\");\n/* harmony import */ var I_workspace_nextjs_backend_src_app_api_routes_route_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(I_workspace_nextjs_backend_src_app_api_routes_route_js__WEBPACK_IMPORTED_MODULE_16__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/routes/route\",\n        pathname: \"/api/routes\",\n        filename: \"route\",\n        bundlePath: \"app/api/routes/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"I:\\\\workspace\\\\nextjs-backend\\\\src\\\\app\\\\api\\\\routes\\\\route.js\",\n    nextConfigOutput,\n    userland: I_workspace_nextjs_backend_src_app_api_routes_route_js__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/routes/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Froutes%2Froute&page=%2Fapi%2Froutes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froutes%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/routes/route.js":
/*!*************************************!*\
  !*** ./src/app/api/routes/route.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * 路由管理API\n * 提供路由查询、统计、验证等功能\n */ \nconst { NextResponse } = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/api/server.js\");\nconst { routeManager } = __webpack_require__(/*! ../../../lib/route-manager */ \"(rsc)/./src/lib/route-manager.js\");\n// 初始化路由管理器\nrouteManager.init();\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action') || 'list';\n        const category = searchParams.get('category');\n        const source = searchParams.get('source');\n        switch(action){\n            case 'list':\n                // 获取所有路由列表\n                const routes = routeManager.getAllRoutes();\n                return NextResponse.json({\n                    success: true,\n                    data: routes,\n                    total: routes.length\n                });\n            case 'stats':\n                // 获取路由统计信息\n                const stats = routeManager.getStats();\n                return NextResponse.json({\n                    success: true,\n                    data: stats\n                });\n            case 'find':\n                // 查找特定路由\n                if (!source) {\n                    return NextResponse.json({\n                        error: '缺少source参数'\n                    }, {\n                        status: 400\n                    });\n                }\n                const route = routeManager.findRoute(source);\n                return NextResponse.json({\n                    success: true,\n                    data: route,\n                    found: !!route\n                });\n            case 'validate':\n                // 验证路由配置\n                const validation = routeManager.validateRoutes();\n                return NextResponse.json({\n                    success: true,\n                    data: validation\n                });\n            case 'docs':\n                // 生成路由文档\n                const docs = routeManager.generateDocs();\n                return new Response(docs, {\n                    status: 200,\n                    headers: {\n                        'Content-Type': 'text/markdown; charset=utf-8'\n                    }\n                });\n            case 'category':\n                // 按分类获取路由\n                if (!category) {\n                    return NextResponse.json({\n                        error: '缺少category参数'\n                    }, {\n                        status: 400\n                    });\n                }\n                const allRoutes = routeManager.getAllRoutes();\n                const stats_cat = routeManager.getStats();\n                const categoryRoutes = allRoutes.filter((r)=>stats_cat.categories[category] && stats_cat.categories[category].includes(r.source));\n                return NextResponse.json({\n                    success: true,\n                    category,\n                    data: categoryRoutes,\n                    total: categoryRoutes.length\n                });\n            default:\n                return NextResponse.json({\n                    error: 'Invalid action',\n                    availableActions: [\n                        'list',\n                        'stats',\n                        'find',\n                        'validate',\n                        'docs',\n                        'category'\n                    ]\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('❌ 路由管理API错误:', error.message);\n        return NextResponse.json({\n            error: error.message,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, routes, source, destination, category } = body;\n        switch(action){\n            case 'add':\n                // 添加单个路由\n                if (!source || !destination) {\n                    return NextResponse.json({\n                        error: 'source和destination参数必需'\n                    }, {\n                        status: 400\n                    });\n                }\n                const success = routeManager.addRoute(source, destination, category);\n                return NextResponse.json({\n                    success,\n                    message: success ? '路由添加成功' : '路由添加失败',\n                    data: {\n                        source,\n                        destination,\n                        category\n                    }\n                });\n            case 'batch-add':\n                // 批量添加路由\n                if (!routes || !Array.isArray(routes)) {\n                    return NextResponse.json({\n                        error: 'routes参数必需且必须是数组'\n                    }, {\n                        status: 400\n                    });\n                }\n                const batchSuccess = routeManager.addRoutes(routes);\n                return NextResponse.json({\n                    success: batchSuccess,\n                    message: batchSuccess ? '批量路由添加成功' : '批量路由添加失败',\n                    data: {\n                        count: routes.length\n                    }\n                });\n            default:\n                return NextResponse.json({\n                    error: 'Invalid action',\n                    availableActions: [\n                        'add',\n                        'batch-add'\n                    ]\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('❌ 路由管理API错误:', error.message);\n        return NextResponse.json({\n            error: error.message,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\nmodule.exports = {\n    GET,\n    POST\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9yb3V0ZXMvcm91dGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7OztDQUdDO0FBRUQsTUFBTSxFQUFFQSxZQUFZLEVBQUUsR0FBR0MsbUJBQU9BLENBQUMsOEhBQWE7QUFDOUMsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBR0QsbUJBQU9BLENBQUMsb0VBQTRCO0FBRTdELFdBQVc7QUFDWEMsYUFBYUMsSUFBSTtBQUVqQixlQUFlQyxJQUFJQyxPQUFPO0lBQ3hCLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlGLFFBQVFHLEdBQUc7UUFDNUMsTUFBTUMsU0FBU0gsYUFBYUksR0FBRyxDQUFDLGFBQWE7UUFDN0MsTUFBTUMsV0FBV0wsYUFBYUksR0FBRyxDQUFDO1FBQ2xDLE1BQU1FLFNBQVNOLGFBQWFJLEdBQUcsQ0FBQztRQUVoQyxPQUFRRDtZQUNOLEtBQUs7Z0JBQ0gsV0FBVztnQkFDWCxNQUFNSSxTQUFTWCxhQUFhWSxZQUFZO2dCQUN4QyxPQUFPZCxhQUFhZSxJQUFJLENBQUM7b0JBQ3ZCQyxTQUFTO29CQUNUQyxNQUFNSjtvQkFDTkssT0FBT0wsT0FBT00sTUFBTTtnQkFDdEI7WUFFRixLQUFLO2dCQUNILFdBQVc7Z0JBQ1gsTUFBTUMsUUFBUWxCLGFBQWFtQixRQUFRO2dCQUNuQyxPQUFPckIsYUFBYWUsSUFBSSxDQUFDO29CQUN2QkMsU0FBUztvQkFDVEMsTUFBTUc7Z0JBQ1I7WUFFRixLQUFLO2dCQUNILFNBQVM7Z0JBQ1QsSUFBSSxDQUFDUixRQUFRO29CQUNYLE9BQU9aLGFBQWFlLElBQUksQ0FBQzt3QkFDdkJPLE9BQU87b0JBQ1QsR0FBRzt3QkFBRUMsUUFBUTtvQkFBSTtnQkFDbkI7Z0JBRUEsTUFBTUMsUUFBUXRCLGFBQWF1QixTQUFTLENBQUNiO2dCQUNyQyxPQUFPWixhQUFhZSxJQUFJLENBQUM7b0JBQ3ZCQyxTQUFTO29CQUNUQyxNQUFNTztvQkFDTkUsT0FBTyxDQUFDLENBQUNGO2dCQUNYO1lBRUYsS0FBSztnQkFDSCxTQUFTO2dCQUNULE1BQU1HLGFBQWF6QixhQUFhMEIsY0FBYztnQkFDOUMsT0FBTzVCLGFBQWFlLElBQUksQ0FBQztvQkFDdkJDLFNBQVM7b0JBQ1RDLE1BQU1VO2dCQUNSO1lBRUYsS0FBSztnQkFDSCxTQUFTO2dCQUNULE1BQU1FLE9BQU8zQixhQUFhNEIsWUFBWTtnQkFDdEMsT0FBTyxJQUFJQyxTQUFTRixNQUFNO29CQUN4Qk4sUUFBUTtvQkFDUlMsU0FBUzt3QkFDUCxnQkFBZ0I7b0JBQ2xCO2dCQUNGO1lBRUYsS0FBSztnQkFDSCxVQUFVO2dCQUNWLElBQUksQ0FBQ3JCLFVBQVU7b0JBQ2IsT0FBT1gsYUFBYWUsSUFBSSxDQUFDO3dCQUN2Qk8sT0FBTztvQkFDVCxHQUFHO3dCQUFFQyxRQUFRO29CQUFJO2dCQUNuQjtnQkFFQSxNQUFNVSxZQUFZL0IsYUFBYVksWUFBWTtnQkFDM0MsTUFBTW9CLFlBQVloQyxhQUFhbUIsUUFBUTtnQkFDdkMsTUFBTWMsaUJBQWlCRixVQUFVRyxNQUFNLENBQUNDLENBQUFBLElBQ3RDSCxVQUFVSSxVQUFVLENBQUMzQixTQUFTLElBQzlCdUIsVUFBVUksVUFBVSxDQUFDM0IsU0FBUyxDQUFDNEIsUUFBUSxDQUFDRixFQUFFekIsTUFBTTtnQkFHbEQsT0FBT1osYUFBYWUsSUFBSSxDQUFDO29CQUN2QkMsU0FBUztvQkFDVEw7b0JBQ0FNLE1BQU1rQjtvQkFDTmpCLE9BQU9pQixlQUFlaEIsTUFBTTtnQkFDOUI7WUFFRjtnQkFDRSxPQUFPbkIsYUFBYWUsSUFBSSxDQUFDO29CQUN2Qk8sT0FBTztvQkFDUGtCLGtCQUFrQjt3QkFBQzt3QkFBUTt3QkFBUzt3QkFBUTt3QkFBWTt3QkFBUTtxQkFBVztnQkFDN0UsR0FBRztvQkFBRWpCLFFBQVE7Z0JBQUk7UUFDckI7SUFFRixFQUFFLE9BQU9ELE9BQU87UUFDZG1CLFFBQVFuQixLQUFLLENBQUMsZ0JBQWdCQSxNQUFNb0IsT0FBTztRQUMzQyxPQUFPMUMsYUFBYWUsSUFBSSxDQUFDO1lBQ3ZCTyxPQUFPQSxNQUFNb0IsT0FBTztZQUNwQkMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1FBQ25DLEdBQUc7WUFBRXRCLFFBQVE7UUFBSTtJQUNuQjtBQUNGO0FBRUEsZUFBZXVCLEtBQUt6QyxPQUFPO0lBQ3pCLElBQUk7UUFDRixNQUFNMEMsT0FBTyxNQUFNMUMsUUFBUVUsSUFBSTtRQUMvQixNQUFNLEVBQUVOLE1BQU0sRUFBRUksTUFBTSxFQUFFRCxNQUFNLEVBQUVvQyxXQUFXLEVBQUVyQyxRQUFRLEVBQUUsR0FBR29DO1FBRTFELE9BQVF0QztZQUNOLEtBQUs7Z0JBQ0gsU0FBUztnQkFDVCxJQUFJLENBQUNHLFVBQVUsQ0FBQ29DLGFBQWE7b0JBQzNCLE9BQU9oRCxhQUFhZSxJQUFJLENBQUM7d0JBQ3ZCTyxPQUFPO29CQUNULEdBQUc7d0JBQUVDLFFBQVE7b0JBQUk7Z0JBQ25CO2dCQUVBLE1BQU1QLFVBQVVkLGFBQWErQyxRQUFRLENBQUNyQyxRQUFRb0MsYUFBYXJDO2dCQUMzRCxPQUFPWCxhQUFhZSxJQUFJLENBQUM7b0JBQ3ZCQztvQkFDQTBCLFNBQVMxQixVQUFVLFdBQVc7b0JBQzlCQyxNQUFNO3dCQUFFTDt3QkFBUW9DO3dCQUFhckM7b0JBQVM7Z0JBQ3hDO1lBRUYsS0FBSztnQkFDSCxTQUFTO2dCQUNULElBQUksQ0FBQ0UsVUFBVSxDQUFDcUMsTUFBTUMsT0FBTyxDQUFDdEMsU0FBUztvQkFDckMsT0FBT2IsYUFBYWUsSUFBSSxDQUFDO3dCQUN2Qk8sT0FBTztvQkFDVCxHQUFHO3dCQUFFQyxRQUFRO29CQUFJO2dCQUNuQjtnQkFFQSxNQUFNNkIsZUFBZWxELGFBQWFtRCxTQUFTLENBQUN4QztnQkFDNUMsT0FBT2IsYUFBYWUsSUFBSSxDQUFDO29CQUN2QkMsU0FBU29DO29CQUNUVixTQUFTVSxlQUFlLGFBQWE7b0JBQ3JDbkMsTUFBTTt3QkFBRXFDLE9BQU96QyxPQUFPTSxNQUFNO29CQUFDO2dCQUMvQjtZQUVGO2dCQUNFLE9BQU9uQixhQUFhZSxJQUFJLENBQUM7b0JBQ3ZCTyxPQUFPO29CQUNQa0Isa0JBQWtCO3dCQUFDO3dCQUFPO3FCQUFZO2dCQUN4QyxHQUFHO29CQUFFakIsUUFBUTtnQkFBSTtRQUNyQjtJQUVGLEVBQUUsT0FBT0QsT0FBTztRQUNkbUIsUUFBUW5CLEtBQUssQ0FBQyxnQkFBZ0JBLE1BQU1vQixPQUFPO1FBQzNDLE9BQU8xQyxhQUFhZSxJQUFJLENBQUM7WUFDdkJPLE9BQU9BLE1BQU1vQixPQUFPO1lBQ3BCQyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7UUFDbkMsR0FBRztZQUFFdEIsUUFBUTtRQUFJO0lBQ25CO0FBQ0Y7QUFFQWdDLE9BQU9DLE9BQU8sR0FBRztJQUFFcEQ7SUFBSzBDO0FBQUsiLCJzb3VyY2VzIjpbIkk6XFx3b3Jrc3BhY2VcXG5leHRqcy1iYWNrZW5kXFxzcmNcXGFwcFxcYXBpXFxyb3V0ZXNcXHJvdXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog6Lev55Sx566h55CGQVBJXG4gKiDmj5Dkvpvot6/nlLHmn6Xor6LjgIHnu5/orqHjgIHpqozor4HnrYnlip/og71cbiAqL1xuXG5jb25zdCB7IE5leHRSZXNwb25zZSB9ID0gcmVxdWlyZSgnbmV4dC9zZXJ2ZXInKTtcbmNvbnN0IHsgcm91dGVNYW5hZ2VyIH0gPSByZXF1aXJlKCcuLi8uLi8uLi9saWIvcm91dGUtbWFuYWdlcicpO1xuXG4vLyDliJ3lp4vljJbot6/nlLHnrqHnkIblmahcbnJvdXRlTWFuYWdlci5pbml0KCk7XG5cbmFzeW5jIGZ1bmN0aW9uIEdFVChyZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xuICAgIGNvbnN0IGFjdGlvbiA9IHNlYXJjaFBhcmFtcy5nZXQoJ2FjdGlvbicpIHx8ICdsaXN0JztcbiAgICBjb25zdCBjYXRlZ29yeSA9IHNlYXJjaFBhcmFtcy5nZXQoJ2NhdGVnb3J5Jyk7XG4gICAgY29uc3Qgc291cmNlID0gc2VhcmNoUGFyYW1zLmdldCgnc291cmNlJyk7XG5cbiAgICBzd2l0Y2ggKGFjdGlvbikge1xuICAgICAgY2FzZSAnbGlzdCc6XG4gICAgICAgIC8vIOiOt+WPluaJgOaciei3r+eUseWIl+ihqFxuICAgICAgICBjb25zdCByb3V0ZXMgPSByb3V0ZU1hbmFnZXIuZ2V0QWxsUm91dGVzKCk7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBkYXRhOiByb3V0ZXMsXG4gICAgICAgICAgdG90YWw6IHJvdXRlcy5sZW5ndGhcbiAgICAgICAgfSk7XG5cbiAgICAgIGNhc2UgJ3N0YXRzJzpcbiAgICAgICAgLy8g6I635Y+W6Lev55Sx57uf6K6h5L+h5oGvXG4gICAgICAgIGNvbnN0IHN0YXRzID0gcm91dGVNYW5hZ2VyLmdldFN0YXRzKCk7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBkYXRhOiBzdGF0c1xuICAgICAgICB9KTtcblxuICAgICAgY2FzZSAnZmluZCc6XG4gICAgICAgIC8vIOafpeaJvueJueWumui3r+eUsVxuICAgICAgICBpZiAoIXNvdXJjZSkge1xuICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgICBlcnJvcjogJ+e8uuWwkXNvdXJjZeWPguaVsCdcbiAgICAgICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zdCByb3V0ZSA9IHJvdXRlTWFuYWdlci5maW5kUm91dGUoc291cmNlKTtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIGRhdGE6IHJvdXRlLFxuICAgICAgICAgIGZvdW5kOiAhIXJvdXRlXG4gICAgICAgIH0pO1xuXG4gICAgICBjYXNlICd2YWxpZGF0ZSc6XG4gICAgICAgIC8vIOmqjOivgei3r+eUsemFjee9rlxuICAgICAgICBjb25zdCB2YWxpZGF0aW9uID0gcm91dGVNYW5hZ2VyLnZhbGlkYXRlUm91dGVzKCk7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBkYXRhOiB2YWxpZGF0aW9uXG4gICAgICAgIH0pO1xuXG4gICAgICBjYXNlICdkb2NzJzpcbiAgICAgICAgLy8g55Sf5oiQ6Lev55Sx5paH5qGjXG4gICAgICAgIGNvbnN0IGRvY3MgPSByb3V0ZU1hbmFnZXIuZ2VuZXJhdGVEb2NzKCk7XG4gICAgICAgIHJldHVybiBuZXcgUmVzcG9uc2UoZG9jcywge1xuICAgICAgICAgIHN0YXR1czogMjAwLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAndGV4dC9tYXJrZG93bjsgY2hhcnNldD11dGYtOCcsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSk7XG5cbiAgICAgIGNhc2UgJ2NhdGVnb3J5JzpcbiAgICAgICAgLy8g5oyJ5YiG57G76I635Y+W6Lev55SxXG4gICAgICAgIGlmICghY2F0ZWdvcnkpIHtcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgICAgZXJyb3I6ICfnvLrlsJFjYXRlZ29yeeWPguaVsCdcbiAgICAgICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zdCBhbGxSb3V0ZXMgPSByb3V0ZU1hbmFnZXIuZ2V0QWxsUm91dGVzKCk7XG4gICAgICAgIGNvbnN0IHN0YXRzX2NhdCA9IHJvdXRlTWFuYWdlci5nZXRTdGF0cygpO1xuICAgICAgICBjb25zdCBjYXRlZ29yeVJvdXRlcyA9IGFsbFJvdXRlcy5maWx0ZXIociA9PiBcbiAgICAgICAgICBzdGF0c19jYXQuY2F0ZWdvcmllc1tjYXRlZ29yeV0gJiYgXG4gICAgICAgICAgc3RhdHNfY2F0LmNhdGVnb3JpZXNbY2F0ZWdvcnldLmluY2x1ZGVzKHIuc291cmNlKVxuICAgICAgICApO1xuICAgICAgICBcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIGNhdGVnb3J5LFxuICAgICAgICAgIGRhdGE6IGNhdGVnb3J5Um91dGVzLFxuICAgICAgICAgIHRvdGFsOiBjYXRlZ29yeVJvdXRlcy5sZW5ndGhcbiAgICAgICAgfSk7XG5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgZXJyb3I6ICdJbnZhbGlkIGFjdGlvbicsXG4gICAgICAgICAgYXZhaWxhYmxlQWN0aW9uczogWydsaXN0JywgJ3N0YXRzJywgJ2ZpbmQnLCAndmFsaWRhdGUnLCAnZG9jcycsICdjYXRlZ29yeSddXG4gICAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIOi3r+eUseeuoeeQhkFQSemUmeivrzonLCBlcnJvci5tZXNzYWdlKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuICAgIGNvbnN0IHsgYWN0aW9uLCByb3V0ZXMsIHNvdXJjZSwgZGVzdGluYXRpb24sIGNhdGVnb3J5IH0gPSBib2R5O1xuXG4gICAgc3dpdGNoIChhY3Rpb24pIHtcbiAgICAgIGNhc2UgJ2FkZCc6XG4gICAgICAgIC8vIOa3u+WKoOWNleS4qui3r+eUsVxuICAgICAgICBpZiAoIXNvdXJjZSB8fCAhZGVzdGluYXRpb24pIHtcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgICAgZXJyb3I6ICdzb3VyY2XlkoxkZXN0aW5hdGlvbuWPguaVsOW/hemcgCdcbiAgICAgICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3Qgc3VjY2VzcyA9IHJvdXRlTWFuYWdlci5hZGRSb3V0ZShzb3VyY2UsIGRlc3RpbmF0aW9uLCBjYXRlZ29yeSk7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzcyxcbiAgICAgICAgICBtZXNzYWdlOiBzdWNjZXNzID8gJ+i3r+eUsea3u+WKoOaIkOWKnycgOiAn6Lev55Sx5re75Yqg5aSx6LSlJyxcbiAgICAgICAgICBkYXRhOiB7IHNvdXJjZSwgZGVzdGluYXRpb24sIGNhdGVnb3J5IH1cbiAgICAgICAgfSk7XG5cbiAgICAgIGNhc2UgJ2JhdGNoLWFkZCc6XG4gICAgICAgIC8vIOaJuemHj+a3u+WKoOi3r+eUsVxuICAgICAgICBpZiAoIXJvdXRlcyB8fCAhQXJyYXkuaXNBcnJheShyb3V0ZXMpKSB7XG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICAgIGVycm9yOiAncm91dGVz5Y+C5pWw5b+F6ZyA5LiU5b+F6aG75piv5pWw57uEJ1xuICAgICAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBiYXRjaFN1Y2Nlc3MgPSByb3V0ZU1hbmFnZXIuYWRkUm91dGVzKHJvdXRlcyk7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogYmF0Y2hTdWNjZXNzLFxuICAgICAgICAgIG1lc3NhZ2U6IGJhdGNoU3VjY2VzcyA/ICfmibnph4/ot6/nlLHmt7vliqDmiJDlip8nIDogJ+aJuemHj+i3r+eUsea3u+WKoOWksei0pScsXG4gICAgICAgICAgZGF0YTogeyBjb3VudDogcm91dGVzLmxlbmd0aCB9XG4gICAgICAgIH0pO1xuXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIGVycm9yOiAnSW52YWxpZCBhY3Rpb24nLFxuICAgICAgICAgIGF2YWlsYWJsZUFjdGlvbnM6IFsnYWRkJywgJ2JhdGNoLWFkZCddXG4gICAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIOi3r+eUseeuoeeQhkFQSemUmeivrzonLCBlcnJvci5tZXNzYWdlKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7IEdFVCwgUE9TVCB9O1xuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInJlcXVpcmUiLCJyb3V0ZU1hbmFnZXIiLCJpbml0IiwiR0VUIiwicmVxdWVzdCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsImFjdGlvbiIsImdldCIsImNhdGVnb3J5Iiwic291cmNlIiwicm91dGVzIiwiZ2V0QWxsUm91dGVzIiwianNvbiIsInN1Y2Nlc3MiLCJkYXRhIiwidG90YWwiLCJsZW5ndGgiLCJzdGF0cyIsImdldFN0YXRzIiwiZXJyb3IiLCJzdGF0dXMiLCJyb3V0ZSIsImZpbmRSb3V0ZSIsImZvdW5kIiwidmFsaWRhdGlvbiIsInZhbGlkYXRlUm91dGVzIiwiZG9jcyIsImdlbmVyYXRlRG9jcyIsIlJlc3BvbnNlIiwiaGVhZGVycyIsImFsbFJvdXRlcyIsInN0YXRzX2NhdCIsImNhdGVnb3J5Um91dGVzIiwiZmlsdGVyIiwiciIsImNhdGVnb3JpZXMiLCJpbmNsdWRlcyIsImF2YWlsYWJsZUFjdGlvbnMiLCJjb25zb2xlIiwibWVzc2FnZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsIlBPU1QiLCJib2R5IiwiZGVzdGluYXRpb24iLCJhZGRSb3V0ZSIsIkFycmF5IiwiaXNBcnJheSIsImJhdGNoU3VjY2VzcyIsImFkZFJvdXRlcyIsImNvdW50IiwibW9kdWxlIiwiZXhwb3J0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/routes/route.js\n");

/***/ }),

/***/ "(rsc)/./src/config/routes.js":
/*!******************************!*\
  !*** ./src/config/routes.js ***!
  \******************************/
/***/ ((module) => {

"use strict";
eval("/**\n * 路由配置文件\n * 统一管理所有路由重写规则\n */ // FB相关路由配置\n\nconst fbRoutes = [\n    // 题目相关\n    {\n        source: '/fbtimu',\n        destination: '/api/fb/timu'\n    },\n    {\n        source: '/fbgettimu',\n        destination: '/api/fb/timu'\n    },\n    {\n        source: '/fbgethtimu',\n        destination: '/api/fb/gethtimu'\n    },\n    {\n        source: '/gethtimu',\n        destination: '/api/fb/gethtimu'\n    },\n    // 评论和排名\n    {\n        source: '/pinglun',\n        destination: '/api/fb/pinglun'\n    },\n    {\n        source: '/fbpl',\n        destination: '/api/fb/pinglun'\n    },\n    {\n        source: '/gettimurank',\n        destination: '/api/fb/gettimurank'\n    },\n    // 提交和状态管理\n    {\n        source: '/fbsubmit',\n        destination: '/api/fb/fbsubmit'\n    },\n    {\n        source: '/fbremeber',\n        destination: '/api/fb/fbremeber'\n    },\n    {\n        source: '/fbrecoverpage',\n        destination: '/api/fb/fbrecoverpage'\n    },\n    // 数据操作\n    {\n        source: '/setnull',\n        destination: '/api/fb/setnull'\n    },\n    {\n        source: '/updateidsnull',\n        destination: '/api/fb/updateidsnull'\n    }\n];\n// Home相关路由配置\nconst homeRoutes = [\n    {\n        source: '/showip',\n        destination: '/api/home/<USER>'\n    },\n    {\n        source: '/gitpull',\n        destination: '/api/home/<USER>'\n    },\n    {\n        source: '/button',\n        destination: '/api/home/<USER>'\n    },\n    {\n        source: '/clashsw',\n        destination: '/api/home/<USER>'\n    },\n    {\n        source: '/health',\n        destination: '/api/home/<USER>'\n    }\n];\n// 测试和工具路由配置\nconst utilRoutes = [\n    {\n        source: '/test-db',\n        destination: '/api/test-db'\n    },\n    {\n        source: '/websocket-test',\n        destination: '/api/websocket/test'\n    }\n];\n// 合并所有路由配置\nconst allRoutes = [\n    ...fbRoutes,\n    ...homeRoutes,\n    ...utilRoutes\n];\n/**\n * 获取所有路由重写配置\n * @returns {Array} 路由重写配置数组\n */ function getRouteRewrites() {\n    return allRoutes;\n}\n/**\n * 添加新的路由配置\n * @param {Object} route 路由配置对象 {source, destination}\n */ function addRoute(route) {\n    if (!route.source || !route.destination) {\n        throw new Error('路由配置必须包含 source 和 destination');\n    }\n    allRoutes.push(route);\n}\n/**\n * 批量添加路由配置\n * @param {Array} routes 路由配置数组\n */ function addRoutes(routes) {\n    routes.forEach((route)=>addRoute(route));\n}\n/**\n * 根据源路径查找路由配置\n * @param {string} source 源路径\n * @returns {Object|null} 路由配置对象或null\n */ function findRoute(source) {\n    return allRoutes.find((route)=>route.source === source) || null;\n}\n/**\n * 获取路由统计信息\n * @returns {Object} 路由统计信息\n */ function getRouteStats() {\n    return {\n        total: allRoutes.length,\n        fb: fbRoutes.length,\n        home: homeRoutes.length,\n        util: utilRoutes.length,\n        categories: {\n            fb: fbRoutes.map((r)=>r.source),\n            home: homeRoutes.map((r)=>r.source),\n            util: utilRoutes.map((r)=>r.source)\n        }\n    };\n}\nmodule.exports = {\n    getRouteRewrites,\n    addRoute,\n    addRoutes,\n    findRoute,\n    getRouteStats,\n    // 导出分类路由供单独使用\n    fbRoutes,\n    homeRoutes,\n    utilRoutes\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/routes.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/route-manager.js":
/*!**********************************!*\
  !*** ./src/lib/route-manager.js ***!
  \**********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * 路由管理工具\n * 提供路由注册、查询、统计等功能\n */ \nconst { getRouteRewrites, addRoute, addRoutes, findRoute, getRouteStats } = __webpack_require__(/*! ../config/routes */ \"(rsc)/./src/config/routes.js\");\nclass RouteManager {\n    constructor(){\n        this.initialized = false;\n    }\n    /**\n   * 初始化路由管理器\n   */ init() {\n        if (this.initialized) return;\n        console.log('🚀 路由管理器初始化...');\n        const stats = getRouteStats();\n        console.log(`📊 路由统计: 总计${stats.total}个路由`);\n        console.log(`   - FB路由: ${stats.fb}个`);\n        console.log(`   - Home路由: ${stats.home}个`);\n        console.log(`   - 工具路由: ${stats.util}个`);\n        this.initialized = true;\n    }\n    /**\n   * 获取所有路由\n   */ getAllRoutes() {\n        return getRouteRewrites();\n    }\n    /**\n   * 查找路由\n   */ findRoute(source) {\n        return findRoute(source);\n    }\n    /**\n   * 添加单个路由\n   */ addRoute(source, destination, category = 'custom') {\n        try {\n            addRoute({\n                source,\n                destination,\n                category\n            });\n            console.log(`✅ 路由添加成功: ${source} -> ${destination}`);\n            return true;\n        } catch (error) {\n            console.error(`❌ 路由添加失败: ${error.message}`);\n            return false;\n        }\n    }\n    /**\n   * 批量添加路由\n   */ addRoutes(routes) {\n        try {\n            addRoutes(routes);\n            console.log(`✅ 批量路由添加成功: ${routes.length}个路由`);\n            return true;\n        } catch (error) {\n            console.error(`❌ 批量路由添加失败: ${error.message}`);\n            return false;\n        }\n    }\n    /**\n   * 获取路由统计信息\n   */ getStats() {\n        return getRouteStats();\n    }\n    /**\n   * 验证路由配置\n   */ validateRoutes() {\n        const routes = this.getAllRoutes();\n        const issues = [];\n        const sourceMap = new Map();\n        routes.forEach((route, index)=>{\n            // 检查必需字段\n            if (!route.source || !route.destination) {\n                issues.push({\n                    type: 'missing_fields',\n                    index,\n                    route,\n                    message: '缺少source或destination字段'\n                });\n            }\n            // 检查重复的source\n            if (sourceMap.has(route.source)) {\n                issues.push({\n                    type: 'duplicate_source',\n                    index,\n                    route,\n                    duplicate: sourceMap.get(route.source),\n                    message: `重复的source路径: ${route.source}`\n                });\n            } else {\n                sourceMap.set(route.source, {\n                    index,\n                    route\n                });\n            }\n            // 检查路径格式\n            if (route.source && !route.source.startsWith('/')) {\n                issues.push({\n                    type: 'invalid_format',\n                    index,\n                    route,\n                    message: `source路径应以/开头: ${route.source}`\n                });\n            }\n            if (route.destination && !route.destination.startsWith('/')) {\n                issues.push({\n                    type: 'invalid_format',\n                    index,\n                    route,\n                    message: `destination路径应以/开头: ${route.destination}`\n                });\n            }\n        });\n        return {\n            valid: issues.length === 0,\n            issues,\n            totalRoutes: routes.length\n        };\n    }\n    /**\n   * 打印路由信息\n   */ printRoutes(category = null) {\n        const routes = this.getAllRoutes();\n        const stats = this.getStats();\n        console.log('\\n📋 路由配置信息:');\n        console.log('='.repeat(50));\n        if (category) {\n            const categoryRoutes = routes.filter((r)=>stats.categories[category] && stats.categories[category].includes(r.source));\n            console.log(`📂 ${category.toUpperCase()}路由 (${categoryRoutes.length}个):`);\n            categoryRoutes.forEach((route)=>{\n                console.log(`   ${route.source} -> ${route.destination}`);\n            });\n        } else {\n            Object.keys(stats.categories).forEach((cat)=>{\n                console.log(`📂 ${cat.toUpperCase()}路由 (${stats.categories[cat].length}个):`);\n                stats.categories[cat].forEach((source)=>{\n                    const route = this.findRoute(source);\n                    if (route) {\n                        console.log(`   ${route.source} -> ${route.destination}`);\n                    }\n                });\n                console.log('');\n            });\n        }\n        console.log('='.repeat(50));\n        console.log(`📊 总计: ${stats.total}个路由\\n`);\n    }\n    /**\n   * 生成路由文档\n   */ generateDocs() {\n        const routes = this.getAllRoutes();\n        const stats = this.getStats();\n        let docs = '# API路由文档\\n\\n';\n        docs += `> 自动生成于: ${new Date().toLocaleString()}\\n\\n`;\n        docs += `## 路由统计\\n\\n`;\n        docs += `- 总路由数: ${stats.total}\\n`;\n        docs += `- FB相关: ${stats.fb}\\n`;\n        docs += `- Home相关: ${stats.home}\\n`;\n        docs += `- 工具相关: ${stats.util}\\n\\n`;\n        Object.keys(stats.categories).forEach((category)=>{\n            docs += `## ${category.toUpperCase()}路由\\n\\n`;\n            docs += '| 源路径 | 目标路径 |\\n';\n            docs += '|--------|----------|\\n';\n            stats.categories[category].forEach((source)=>{\n                const route = this.findRoute(source);\n                if (route) {\n                    docs += `| \\`${route.source}\\` | \\`${route.destination}\\` |\\n`;\n                }\n            });\n            docs += '\\n';\n        });\n        return docs;\n    }\n}\n// 创建单例实例\nconst routeManager = new RouteManager();\nmodule.exports = {\n    RouteManager,\n    routeManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/route-manager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.4.5_react-dom@19.1.0_react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Froutes%2Froute&page=%2Fapi%2Froutes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Froutes%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();