/**
 * 路由配置文件
 * 统一管理所有路由重写规则
 */

// FB相关路由配置
const fbRoutes = [
  // 题目相关
  { source: '/fbtimu', destination: '/api/fb/timu' },
  { source: '/fbgettimu', destination: '/api/fb/timu' },
  { source: '/fbgethtimu', destination: '/api/fb/gethtimu' },
  { source: '/gethtimu', destination: '/api/fb/gethtimu' },
  
  // 评论和排名
  { source: '/pinglun', destination: '/api/fb/pinglun' },
  { source: '/fbpl', destination: '/api/fb/pinglun' },
  { source: '/gettimurank', destination: '/api/fb/gettimurank' },
  
  // 提交和状态管理
  { source: '/fbsubmit', destination: '/api/fb/fbsubmit' },
  { source: '/fbremeber', destination: '/api/fb/fbremeber' },
  { source: '/fbrecoverpage', destination: '/api/fb/fbrecoverpage' },
  
  // 数据操作
  { source: '/setnull', destination: '/api/fb/setnull' },
  { source: '/updateidsnull', destination: '/api/fb/updateidsnull' },
  
  // 其他FB功能（可扩展）
  // { source: '/fbcate', destination: '/api/fb/cate' },
  // { source: '/fbsycate', destination: '/api/fb/sycate' },
  // { source: '/fbcookie', destination: '/api/fb/cookie' },
];

// Home相关路由配置
const homeRoutes = [
  { source: '/showip', destination: '/api/home/<USER>' },
  { source: '/gitpull', destination: '/api/home/<USER>' },
  { source: '/button', destination: '/api/home/<USER>' },
  { source: '/clashsw', destination: '/api/home/<USER>' },
  { source: '/health', destination: '/api/home/<USER>' },
];

// 测试和工具路由配置
const utilRoutes = [
  { source: '/test-db', destination: '/api/test-db' },
  { source: '/websocket-test', destination: '/api/websocket/test' },
];

// 合并所有路由配置
const allRoutes = [
  ...fbRoutes,
  ...homeRoutes,
  ...utilRoutes,
];

/**
 * 获取所有路由重写配置
 * @returns {Array} 路由重写配置数组
 */
function getRouteRewrites() {
  return allRoutes;
}

/**
 * 添加新的路由配置
 * @param {Object} route 路由配置对象 {source, destination}
 */
function addRoute(route) {
  if (!route.source || !route.destination) {
    throw new Error('路由配置必须包含 source 和 destination');
  }
  allRoutes.push(route);
}

/**
 * 批量添加路由配置
 * @param {Array} routes 路由配置数组
 */
function addRoutes(routes) {
  routes.forEach(route => addRoute(route));
}

/**
 * 根据源路径查找路由配置
 * @param {string} source 源路径
 * @returns {Object|null} 路由配置对象或null
 */
function findRoute(source) {
  return allRoutes.find(route => route.source === source) || null;
}

/**
 * 获取路由统计信息
 * @returns {Object} 路由统计信息
 */
function getRouteStats() {
  return {
    total: allRoutes.length,
    fb: fbRoutes.length,
    home: homeRoutes.length,
    util: utilRoutes.length,
    categories: {
      fb: fbRoutes.map(r => r.source),
      home: homeRoutes.map(r => r.source),
      util: utilRoutes.map(r => r.source),
    }
  };
}

module.exports = {
  getRouteRewrites,
  addRoute,
  addRoutes,
  findRoute,
  getRouteStats,
  // 导出分类路由供单独使用
  fbRoutes,
  homeRoutes,
  utilRoutes,
};
