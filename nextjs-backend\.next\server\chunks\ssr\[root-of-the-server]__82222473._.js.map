{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/workspace/nextjs-backend/src/app/page.js"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-100\">\n      <div className=\"text-center\">\n        <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">\n          NextJS Backend Server\n        </h1>\n        <p className=\"text-lg text-gray-600 mb-8\">\n          High-performance backend API server\n        </p>\n        <div className=\"space-y-2 text-sm text-gray-500\">\n          <p>API endpoints available at:</p>\n          <code className=\"bg-gray-200 px-2 py-1 rounded\">/api/*</code>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,mQAAC;QAAI,WAAU;kBACb,cAAA,mQAAC;YAAI,WAAU;;8BACb,mQAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAGtD,mQAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,mQAAC;oBAAI,WAAU;;sCACb,mQAAC;sCAAE;;;;;;sCACH,mQAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;;AAK1D", "debugId": null}}]}