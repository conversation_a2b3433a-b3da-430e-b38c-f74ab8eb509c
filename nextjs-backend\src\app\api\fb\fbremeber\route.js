/**
 * 粉笔页面状态保存API
 * 迁移自egg项目的fb.js fbremeber方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function POST(request) {
  try {
    await ensureAppInitialized();
    
    const body = await request.json();
    const { cateid, kjid, type, page } = body;
    
    console.log('💾 保存页面状态 - 参数:', { cateid, kjid, type, page });
    
    if (!kjid || !type || page === undefined) {
      return NextResponse.json({ 
        error: '缺少必要参数 (kjid, type, page)' 
      }, { status: 400 });
    }
    
    // 构建状态数据
    const stateData = {
      cateid: cateid || null,
      kjid,
      type,
      page: parseInt(page),
      timestamp: new Date().toISOString(),
      lastAccess: Date.now()
    };
    
    // 生成状态键
    const stateKey = `page_state:${kjid}:${type}${cateid ? `:${cateid}` : ''}`;
    
    try {
      // 保存到Redis
      await app.redis.set(
        stateKey,
        JSON.stringify(stateData),
        'EX',
        86400 // 24小时过期
      );

      console.log('✅ 页面状态保存成功:', stateKey);

      // 同时保存一个通用的最后访问状态
      const lastStateKey = `last_state:${kjid}`;
      await app.redis.set(
        lastStateKey,
        JSON.stringify(stateData),
        'EX',
        86400
      );
      
      return NextResponse.json({
        success: true,
        message: '页面状态保存成功',
        data: {
          stateKey,
          ...stateData
        }
      });
      
    } catch (redisError) {
      console.error('❌ Redis保存失败:', redisError.message);
      
      // 如果Redis失败，尝试保存到数据库作为备选
      try {
        // 检查是否已存在记录
        const existingState = await app.mysql.get('fbremeber', {
          kjid,
          type,
          cateid: cateid || null
        });
        
        if (existingState) {
          // 更新现有记录
          await app.mysql.update(
            'fbremeber',
            {
              page: parseInt(page),
              timestamp: stateData.timestamp,
              lastAccess: stateData.lastAccess
            },
            {
              where: {
                kjid,
                type,
                cateid: cateid || null
              }
            }
          );
        } else {
          // 创建新记录
          await app.mysql.insert('fbremeber', {
            kjid,
            type,
            cateid: cateid || null,
            page: parseInt(page),
            timestamp: stateData.timestamp,
            lastAccess: stateData.lastAccess
          });
        }
        
        console.log('✅ 页面状态保存到数据库成功');
        
        return NextResponse.json({
          success: true,
          message: '页面状态保存成功 (数据库备选)',
          data: stateData
        });
        
      } catch (dbError) {
        console.error('❌ 数据库保存也失败:', dbError.message);
        throw new Error('状态保存失败');
      }
    }
    
  } catch (error) {
    console.error('❌ fbremeber API错误:', error.message);
    return NextResponse.json({ 
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { POST };
