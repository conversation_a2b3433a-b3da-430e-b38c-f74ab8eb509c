/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/fb/fbremeber/route";
exports.ids = ["app/api/fb/fbremeber/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffb%2Ffbremeber%2Froute&page=%2Fapi%2Ffb%2Ffbremeber%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffb%2Ffbremeber%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffb%2Ffbremeber%2Froute&page=%2Fapi%2Ffb%2Ffbremeber%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffb%2Ffbremeber%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var I_workspace_nextjs_backend_src_app_api_fb_fbremeber_route_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/fb/fbremeber/route.js */ \"(rsc)/./src/app/api/fb/fbremeber/route.js\");\n/* harmony import */ var I_workspace_nextjs_backend_src_app_api_fb_fbremeber_route_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(I_workspace_nextjs_backend_src_app_api_fb_fbremeber_route_js__WEBPACK_IMPORTED_MODULE_16__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/fb/fbremeber/route\",\n        pathname: \"/api/fb/fbremeber\",\n        filename: \"route\",\n        bundlePath: \"app/api/fb/fbremeber/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"I:\\\\workspace\\\\nextjs-backend\\\\src\\\\app\\\\api\\\\fb\\\\fbremeber\\\\route.js\",\n    nextConfigOutput,\n    userland: I_workspace_nextjs_backend_src_app_api_fb_fbremeber_route_js__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/fb/fbremeber/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffb%2Ffbremeber%2Froute&page=%2Fapi%2Ffb%2Ffbremeber%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffb%2Ffbremeber%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/fb/fbremeber/route.js":
/*!*******************************************!*\
  !*** ./src/app/api/fb/fbremeber/route.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * 粉笔页面状态保存API\n * 迁移自egg项目的fb.js fbremeber方法\n */ \nconst { NextResponse } = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/api/server.js\");\nconst { app } = __webpack_require__(/*! ../../../../lib/app */ \"(rsc)/./src/lib/app.js\");\n// 确保应用已初始化\nlet appInitialized = false;\nasync function ensureAppInitialized() {\n    if (!appInitialized) {\n        await app.init();\n        appInitialized = true;\n    }\n}\nasync function POST(request) {\n    try {\n        await ensureAppInitialized();\n        const body = await request.json();\n        const { cateid, kjid, type, page } = body;\n        console.log('💾 保存页面状态 - 参数:', {\n            cateid,\n            kjid,\n            type,\n            page\n        });\n        if (!kjid || !type || page === undefined) {\n            return NextResponse.json({\n                error: '缺少必要参数 (kjid, type, page)'\n            }, {\n                status: 400\n            });\n        }\n        // 构建状态数据\n        const stateData = {\n            cateid: cateid || null,\n            kjid,\n            type,\n            page: parseInt(page),\n            timestamp: new Date().toISOString(),\n            lastAccess: Date.now()\n        };\n        // 生成状态键\n        const stateKey = `page_state:${kjid}:${type}${cateid ? `:${cateid}` : ''}`;\n        try {\n            // 保存到Redis\n            await app.redis.set(stateKey, JSON.stringify(stateData), 'EX', 86400 // 24小时过期\n            );\n            console.log('✅ 页面状态保存成功:', stateKey);\n            // 同时保存一个通用的最后访问状态\n            const lastStateKey = `last_state:${kjid}`;\n            await app.redis.set(lastStateKey, JSON.stringify(stateData), 'EX', 86400);\n            return NextResponse.json({\n                success: true,\n                message: '页面状态保存成功',\n                data: {\n                    stateKey,\n                    ...stateData\n                }\n            });\n        } catch (redisError) {\n            console.error('❌ Redis保存失败:', redisError.message);\n            // 如果Redis失败，尝试保存到数据库作为备选\n            try {\n                // 检查是否已存在记录\n                const existingState = await app.mysql.get('fbremeber', {\n                    kjid,\n                    cateid: cateid || null\n                });\n                if (existingState) {\n                    // 更新现有记录\n                    await app.mysql.update('fbremeber', {\n                        page: parseInt(page)\n                    }, {\n                        where: {\n                            kjid,\n                            cateid: cateid || null\n                        }\n                    });\n                } else {\n                    // 创建新记录\n                    await app.mysql.insert('fbremeber', {\n                        kjid,\n                        cateid: cateid || null,\n                        page: parseInt(page)\n                    });\n                }\n                console.log('✅ 页面状态保存到数据库成功');\n                return NextResponse.json({\n                    success: true,\n                    message: '页面状态保存成功 (数据库备选)',\n                    data: stateData\n                });\n            } catch (dbError) {\n                console.error('❌ 数据库保存也失败:', dbError.message);\n                throw new Error('状态保存失败');\n            }\n        }\n    } catch (error) {\n        console.error('❌ fbremeber API错误:', error.message);\n        return NextResponse.json({\n            success: false,\n            error: error.message,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\nmodule.exports = {\n    POST\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/fb/fbremeber/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/app.js":
/*!************************!*\
  !*** ./src/lib/app.js ***!
  \************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * 全局应用对象\n * 提供类似egg项目的app.mysql、app.redis等接口\n */ \nconst { initDatabase, mysql } = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.js\");\nconst { initRedis, redis } = __webpack_require__(/*! ./redis */ \"(rsc)/./src/lib/redis.js\");\nclass Application {\n    constructor(){\n        this.mysql = null;\n        this.redis = null;\n        this.initialized = false;\n    }\n    /**\n   * 初始化应用\n   */ async init() {\n        if (this.initialized) {\n            return this;\n        }\n        try {\n            // 初始化数据库\n            initDatabase();\n            this.mysql = mysql;\n            // 初始化Redis\n            initRedis();\n            this.redis = redis;\n            console.log('✅ 应用初始化成功');\n            this.initialized = true;\n            return this;\n        } catch (error) {\n            console.error('❌ 应用初始化失败:', error.message);\n            throw error;\n        }\n    }\n    /**\n   * 健康检查\n   */ async healthCheck() {\n        const health = {\n            status: 'healthy',\n            timestamp: new Date().toISOString(),\n            services: {}\n        };\n        try {\n            // 检查MySQL\n            if (this.mysql) {\n                const mysqlHealth = await this.mysql.healthCheck();\n                health.services.mysql = mysqlHealth;\n            }\n            // 检查Redis\n            if (this.redis) {\n                const redisHealth = await this.redis.healthCheck();\n                health.services.redis = redisHealth;\n                if (redisHealth.status !== 'healthy') {\n                    health.status = 'degraded';\n                }\n            }\n            return health;\n        } catch (error) {\n            health.status = 'unhealthy';\n            health.error = error.message;\n            return health;\n        }\n    }\n    /**\n   * 关闭应用\n   */ async close() {\n        try {\n            if (this.mysql) {\n                await this.mysql.closePool();\n            }\n            if (this.redis) {\n                const { closeRedis } = __webpack_require__(/*! ./redis */ \"(rsc)/./src/lib/redis.js\");\n                await closeRedis();\n            }\n            console.log('✅ 应用已关闭');\n        } catch (error) {\n            console.error('❌ 应用关闭失败:', error.message);\n            throw error;\n        }\n    }\n}\n// 创建全局应用实例\nconst app = new Application();\nmodule.exports = {\n    Application,\n    app\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/app.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/config.js":
/*!***************************!*\
  !*** ./src/lib/config.js ***!
  \***************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * 配置管理工具\n * 支持多环境配置加载和合并\n */ \nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst path = __webpack_require__(/*! path */ \"path\");\n// 配置缓存\nlet configCache = null;\n/**\n * 获取当前环境\n * @returns {string} 环境名称\n */ function getEnvironment() {\n    return \"development\" || 0;\n}\n/**\n * 加载JSON配置文件\n * @param {string} filePath 配置文件路径\n * @returns {object|null} 配置对象或null\n */ function loadConfigFile(filePath) {\n    try {\n        if (fs.existsSync(filePath)) {\n            const content = fs.readFileSync(filePath, 'utf8');\n            return JSON.parse(content);\n        }\n    } catch (error) {\n        console.error(`Failed to load config file ${filePath}:`, error.message);\n    }\n    return null;\n}\n/**\n * 深度合并配置对象\n * @param {object} target 目标对象\n * @param {object} source 源对象\n * @returns {object} 合并后的对象\n */ function deepMerge(target, source) {\n    const result = {\n        ...target\n    };\n    for(const key in source){\n        if (source.hasOwnProperty(key)) {\n            if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key]) && typeof target[key] === 'object' && target[key] !== null && !Array.isArray(target[key])) {\n                result[key] = deepMerge(target[key], source[key]);\n            } else {\n                result[key] = source[key];\n            }\n        }\n    }\n    return result;\n}\n/**\n * 加载配置\n * @param {boolean} forceReload 是否强制重新加载\n * @returns {object} 配置对象\n */ function loadConfig(forceReload = false) {\n    if (configCache && !forceReload) {\n        return configCache;\n    }\n    const env = getEnvironment();\n    const configDir = path.join(process.cwd(), 'config');\n    // 加载默认配置\n    const defaultConfig = loadConfigFile(path.join(configDir, 'default.json')) || {};\n    // 加载环境特定配置\n    let envConfig = {};\n    const envConfigFile = env === 'production' ? 'prod.json' : env === 'development' ? 'dev.json' : `${env}.json`;\n    envConfig = loadConfigFile(path.join(configDir, envConfigFile)) || {};\n    // 合并配置\n    const finalConfig = deepMerge(defaultConfig, envConfig);\n    // 添加环境信息\n    finalConfig.env = env;\n    finalConfig.isDevelopment = env === 'development';\n    finalConfig.isProduction = env === 'production';\n    // 缓存配置\n    configCache = finalConfig;\n    return finalConfig;\n}\n/**\n * 获取配置项\n * @param {string} key 配置键，支持点号分隔的路径\n * @param {any} defaultValue 默认值\n * @returns {any} 配置值\n */ function getConfig(key, defaultValue = undefined) {\n    const config = loadConfig();\n    if (!key) {\n        return config;\n    }\n    const keys = key.split('.');\n    let value = config;\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            return defaultValue;\n        }\n    }\n    return value;\n}\n/**\n * 重新加载配置\n */ function reloadConfig() {\n    configCache = null;\n    return loadConfig(true);\n}\n/**\n * 验证必需的配置项\n * @param {string[]} requiredKeys 必需的配置键数组\n * @throws {Error} 如果缺少必需配置\n */ function validateConfig(requiredKeys = []) {\n    const config = loadConfig();\n    const missing = [];\n    for (const key of requiredKeys){\n        if (getConfig(key) === undefined) {\n            missing.push(key);\n        }\n    }\n    if (missing.length > 0) {\n        throw new Error(`Missing required configuration: ${missing.join(', ')}`);\n    }\n}\nmodule.exports = {\n    getEnvironment,\n    loadConfig,\n    getConfig,\n    reloadConfig,\n    validateConfig,\n    deepMerge\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.js":
/*!*****************************!*\
  !*** ./src/lib/database.js ***!
  \*****************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * 数据库连接管理\n * 提供类似egg项目的app.mysql接口\n */ \nconst mysql = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\nconst { getConfig } = __webpack_require__(/*! ./config */ \"(rsc)/./src/lib/config.js\");\n// 连接池实例\nlet pool = null;\n/**\n * 初始化数据库连接池\n */ function initDatabase() {\n    if (pool) {\n        return pool;\n    }\n    const config = getConfig('database.mysql');\n    pool = mysql.createPool({\n        host: config.host,\n        port: config.port,\n        user: config.user,\n        password: config.password,\n        database: config.database,\n        charset: config.charset || 'utf8mb4',\n        connectionLimit: config.connectionLimit || 10,\n        acquireTimeout: config.acquireTimeout || 60000,\n        timeout: config.timeout || 60000,\n        reconnect: config.reconnect !== false,\n        // 连接池配置\n        queueLimit: 0,\n        // 空闲连接超时时间\n        idleTimeout: 300000,\n        // 连接最大生存时间\n        maxLifetime: 1800000,\n        // 启用多语句查询\n        multipleStatements: true,\n        // 时区设置\n        timezone: '+08:00'\n    });\n    console.log('✅ MySQL连接池初始化成功');\n    return pool;\n}\n/**\n * 获取数据库连接池\n */ function getPool() {\n    if (!pool) {\n        return initDatabase();\n    }\n    return pool;\n}\n/**\n * 执行SQL查询\n * @param {string} sql SQL语句\n * @param {Array} params 参数\n * @returns {Promise<Array>} 查询结果\n */ async function query(sql, params = []) {\n    const pool = getPool();\n    try {\n        const [rows] = await pool.execute(sql, params);\n        return rows;\n    } catch (error) {\n        console.error('❌ SQL查询错误:', error.message);\n        console.error('SQL:', sql);\n        console.error('参数:', params);\n        throw error;\n    }\n}\n/**\n * 查询单条记录 (类似egg的app.mysql.get)\n * @param {string} table 表名\n * @param {Object} where 查询条件\n * @returns {Promise<Object|null>} 查询结果\n */ async function get(table, where = {}) {\n    const whereClause = Object.keys(where).map((key)=>`${key} = ?`).join(' AND ');\n    const values = Object.values(where);\n    const sql = whereClause ? `SELECT * FROM ${table} WHERE ${whereClause} LIMIT 1` : `SELECT * FROM ${table} LIMIT 1`;\n    const rows = await query(sql, values);\n    return rows.length > 0 ? rows[0] : null;\n}\n/**\n * 查询多条记录 (类似egg的app.mysql.select)\n * @param {string} table 表名\n * @param {Object} options 查询选项\n * @returns {Promise<Array>} 查询结果\n */ async function select(table, options = {}) {\n    const { where = {}, orders = [], limit, offset, columns = '*' } = options;\n    let sql = `SELECT ${Array.isArray(columns) ? columns.join(', ') : columns} FROM ${table}`;\n    const values = [];\n    // WHERE条件\n    if (Object.keys(where).length > 0) {\n        const whereClause = Object.keys(where).map((key)=>`${key} = ?`).join(' AND ');\n        sql += ` WHERE ${whereClause}`;\n        values.push(...Object.values(where));\n    }\n    // ORDER BY\n    if (orders.length > 0) {\n        const orderClause = orders.map((order)=>{\n            if (typeof order === 'string') return order;\n            if (Array.isArray(order)) return `${order[0]} ${order[1] || 'ASC'}`;\n            return `${order.column || order.field} ${order.order || 'ASC'}`;\n        }).join(', ');\n        sql += ` ORDER BY ${orderClause}`;\n    }\n    // LIMIT\n    if (limit) {\n        sql += ` LIMIT ${limit}`;\n        if (offset) {\n            sql += ` OFFSET ${offset}`;\n        }\n    }\n    return await query(sql, values);\n}\n/**\n * 插入记录 (类似egg的app.mysql.insert)\n * @param {string} table 表名\n * @param {Object} data 插入数据\n * @returns {Promise<Object>} 插入结果\n */ async function insert(table, data) {\n    const keys = Object.keys(data);\n    const values = Object.values(data);\n    const placeholders = keys.map(()=>'?').join(', ');\n    const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`;\n    const result = await query(sql, values);\n    return {\n        affectedRows: result.affectedRows,\n        insertId: result.insertId,\n        warningCount: result.warningCount\n    };\n}\n/**\n * 更新记录 (类似egg的app.mysql.update)\n * @param {string} table 表名\n * @param {Object} data 更新数据\n * @param {Object} options 更新选项\n * @returns {Promise<Object>} 更新结果\n */ async function update(table, data, options = {}) {\n    const { where = {} } = options;\n    const setClause = Object.keys(data).map((key)=>`${key} = ?`).join(', ');\n    const setValues = Object.values(data);\n    const whereClause = Object.keys(where).map((key)=>`${key} = ?`).join(' AND ');\n    const whereValues = Object.values(where);\n    let sql = `UPDATE ${table} SET ${setClause}`;\n    const values = [\n        ...setValues\n    ];\n    if (whereClause) {\n        sql += ` WHERE ${whereClause}`;\n        values.push(...whereValues);\n    }\n    const result = await query(sql, values);\n    return {\n        affectedRows: result.affectedRows,\n        changedRows: result.changedRows,\n        warningCount: result.warningCount\n    };\n}\n/**\n * 删除记录 (类似egg的app.mysql.delete)\n * @param {string} table 表名\n * @param {Object} where 删除条件\n * @returns {Promise<Object>} 删除结果\n */ async function deleteRecord(table, where = {}) {\n    const whereClause = Object.keys(where).map((key)=>`${key} = ?`).join(' AND ');\n    const values = Object.values(where);\n    if (!whereClause) {\n        throw new Error('删除操作必须提供WHERE条件');\n    }\n    const sql = `DELETE FROM ${table} WHERE ${whereClause}`;\n    const result = await query(sql, values);\n    return {\n        affectedRows: result.affectedRows,\n        warningCount: result.warningCount\n    };\n}\n/**\n * 开始事务\n */ async function beginTransaction() {\n    const connection = await getPool().getConnection();\n    await connection.beginTransaction();\n    return connection;\n}\n/**\n * 提交事务\n */ async function commit(connection) {\n    await connection.commit();\n    connection.release();\n}\n/**\n * 回滚事务\n */ async function rollback(connection) {\n    await connection.rollback();\n    connection.release();\n}\n/**\n * 健康检查\n */ async function healthCheck() {\n    try {\n        await query('SELECT 1');\n        return {\n            status: 'healthy',\n            timestamp: new Date().toISOString()\n        };\n    } catch (error) {\n        return {\n            status: 'unhealthy',\n            error: error.message,\n            timestamp: new Date().toISOString()\n        };\n    }\n}\n/**\n * 关闭连接池\n */ async function closePool() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log('✅ MySQL连接池已关闭');\n    }\n}\n// 创建类似egg的app.mysql对象\nconst mysql_client = {\n    query,\n    get,\n    select,\n    insert,\n    update,\n    delete: deleteRecord,\n    beginTransaction,\n    commit,\n    rollback,\n    healthCheck,\n    closePool,\n    // 获取原始连接池\n    getPool\n};\nmodule.exports = {\n    initDatabase,\n    mysql: mysql_client,\n    healthCheck,\n    closePool\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/redis.js":
/*!**************************!*\
  !*** ./src/lib/redis.js ***!
  \**************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * Redis连接管理\n * 提供类似egg项目的app.redis接口\n */ \nconst Redis = __webpack_require__(/*! ioredis */ \"ioredis\");\nconst { getConfig } = __webpack_require__(/*! ./config */ \"(rsc)/./src/lib/config.js\");\n// Redis客户端实例\nlet redisClient = null;\n/**\n * 初始化Redis连接\n */ function initRedis() {\n    if (redisClient) {\n        return redisClient;\n    }\n    const config = getConfig('redis');\n    // Redis连接配置\n    const redisConfig = {\n        host: config.host,\n        port: config.port,\n        password: config.password || undefined,\n        db: config.db || 0,\n        connectTimeout: config.connectTimeout || 10000,\n        maxRetriesPerRequest: config.maxRetriesPerRequest || 3,\n        retryDelayOnFailover: config.retryDelayOnFailover || 100,\n        enableReadyCheck: config.enableReadyCheck !== false,\n        lazyConnect: config.lazyConnect !== false,\n        // 连接池配置\n        family: 4,\n        keepAlive: true,\n        // 重试配置\n        retryDelayOnClusterDown: 300,\n        retryDelayOnFailover: 100,\n        maxRetriesPerRequest: 3,\n        // 自动重连\n        autoResubscribe: true,\n        autoResendUnfulfilledCommands: true,\n        // 命令超时\n        commandTimeout: 5000,\n        // 键名前缀\n        keyPrefix: config.keyPrefix || ''\n    };\n    redisClient = new Redis(redisConfig);\n    // 连接事件监听\n    redisClient.on('connect', ()=>{\n        console.log('✅ Redis连接成功');\n    });\n    redisClient.on('ready', ()=>{\n        console.log('✅ Redis准备就绪');\n    });\n    redisClient.on('error', (error)=>{\n        console.error('❌ Redis连接错误:', error.message);\n    });\n    redisClient.on('close', ()=>{\n        console.log('⚠️ Redis连接关闭');\n    });\n    redisClient.on('reconnecting', ()=>{\n        console.log('🔄 Redis重新连接中...');\n    });\n    redisClient.on('end', ()=>{\n        console.log('🔚 Redis连接结束');\n    });\n    return redisClient;\n}\n/**\n * 获取Redis客户端\n */ function getRedisClient() {\n    if (!redisClient) {\n        return initRedis();\n    }\n    return redisClient;\n}\n/**\n * 获取值 (类似egg的app.redis.get)\n * @param {string} key 键名\n * @returns {Promise<string|null>} 值\n */ async function get(key) {\n    const client = getRedisClient();\n    try {\n        return await client.get(key);\n    } catch (error) {\n        console.error('❌ Redis GET错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 设置值 (类似egg的app.redis.set)\n * @param {string} key 键名\n * @param {string} value 值\n * @param {number|string} ttl 过期时间（秒）\n * @returns {Promise<string>} 结果\n */ async function set(key, value, ttl) {\n    const client = getRedisClient();\n    try {\n        if (ttl) {\n            return await client.setex(key, ttl, value);\n        } else {\n            return await client.set(key, value);\n        }\n    } catch (error) {\n        console.error('❌ Redis SET错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 删除键 (类似egg的app.redis.del)\n * @param {string|Array} keys 键名或键名数组\n * @returns {Promise<number>} 删除的键数量\n */ async function del(keys) {\n    const client = getRedisClient();\n    try {\n        return await client.del(keys);\n    } catch (error) {\n        console.error('❌ Redis DEL错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 检查键是否存在\n * @param {string} key 键名\n * @returns {Promise<number>} 存在返回1，不存在返回0\n */ async function exists(key) {\n    const client = getRedisClient();\n    try {\n        return await client.exists(key);\n    } catch (error) {\n        console.error('❌ Redis EXISTS错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 设置过期时间\n * @param {string} key 键名\n * @param {number} seconds 过期时间（秒）\n * @returns {Promise<number>} 成功返回1，失败返回0\n */ async function expire(key, seconds) {\n    const client = getRedisClient();\n    try {\n        return await client.expire(key, seconds);\n    } catch (error) {\n        console.error('❌ Redis EXPIRE错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 获取剩余过期时间\n * @param {string} key 键名\n * @returns {Promise<number>} 剩余时间（秒），-1表示永不过期，-2表示键不存在\n */ async function ttl(key) {\n    const client = getRedisClient();\n    try {\n        return await client.ttl(key);\n    } catch (error) {\n        console.error('❌ Redis TTL错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 自增\n * @param {string} key 键名\n * @param {number} increment 增量，默认为1\n * @returns {Promise<number>} 自增后的值\n */ async function incr(key, increment = 1) {\n    const client = getRedisClient();\n    try {\n        if (increment === 1) {\n            return await client.incr(key);\n        } else {\n            return await client.incrby(key, increment);\n        }\n    } catch (error) {\n        console.error('❌ Redis INCR错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 自减\n * @param {string} key 键名\n * @param {number} decrement 减量，默认为1\n * @returns {Promise<number>} 自减后的值\n */ async function decr(key, decrement = 1) {\n    const client = getRedisClient();\n    try {\n        if (decrement === 1) {\n            return await client.decr(key);\n        } else {\n            return await client.decrby(key, decrement);\n        }\n    } catch (error) {\n        console.error('❌ Redis DECR错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 哈希表操作 - 设置字段\n * @param {string} key 键名\n * @param {string} field 字段名\n * @param {string} value 值\n * @returns {Promise<number>} 成功返回1，更新返回0\n */ async function hset(key, field, value) {\n    const client = getRedisClient();\n    try {\n        return await client.hset(key, field, value);\n    } catch (error) {\n        console.error('❌ Redis HSET错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 哈希表操作 - 获取字段\n * @param {string} key 键名\n * @param {string} field 字段名\n * @returns {Promise<string|null>} 字段值\n */ async function hget(key, field) {\n    const client = getRedisClient();\n    try {\n        return await client.hget(key, field);\n    } catch (error) {\n        console.error('❌ Redis HGET错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 哈希表操作 - 获取所有字段\n * @param {string} key 键名\n * @returns {Promise<Object>} 所有字段和值\n */ async function hgetall(key) {\n    const client = getRedisClient();\n    try {\n        return await client.hgetall(key);\n    } catch (error) {\n        console.error('❌ Redis HGETALL错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 列表操作 - 左推入\n * @param {string} key 键名\n * @param {string|Array} values 值\n * @returns {Promise<number>} 列表长度\n */ async function lpush(key, ...values) {\n    const client = getRedisClient();\n    try {\n        return await client.lpush(key, ...values);\n    } catch (error) {\n        console.error('❌ Redis LPUSH错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 列表操作 - 右弹出\n * @param {string} key 键名\n * @returns {Promise<string|null>} 弹出的值\n */ async function rpop(key) {\n    const client = getRedisClient();\n    try {\n        return await client.rpop(key);\n    } catch (error) {\n        console.error('❌ Redis RPOP错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 获取列表范围\n * @param {string} key 键名\n * @param {number} start 开始索引\n * @param {number} stop 结束索引\n * @returns {Promise<Array>} 列表元素\n */ async function lrange(key, start, stop) {\n    const client = getRedisClient();\n    try {\n        return await client.lrange(key, start, stop);\n    } catch (error) {\n        console.error('❌ Redis LRANGE错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 执行原生Redis命令\n * @param {string} command 命令名\n * @param {...any} args 命令参数\n * @returns {Promise<any>} 命令结果\n */ async function sendCommand(command, ...args) {\n    const client = getRedisClient();\n    try {\n        return await client.sendCommand(new Redis.Command(command, args));\n    } catch (error) {\n        console.error(`❌ Redis ${command}错误:`, error.message);\n        throw error;\n    }\n}\n/**\n * Ping测试\n * @returns {Promise<string>} PONG\n */ async function ping() {\n    const client = getRedisClient();\n    try {\n        return await client.ping();\n    } catch (error) {\n        console.error('❌ Redis PING错误:', error.message);\n        throw error;\n    }\n}\n/**\n * 健康检查\n */ async function healthCheck() {\n    try {\n        await ping();\n        return {\n            status: 'healthy',\n            timestamp: new Date().toISOString()\n        };\n    } catch (error) {\n        return {\n            status: 'unhealthy',\n            error: error.message,\n            timestamp: new Date().toISOString()\n        };\n    }\n}\n/**\n * 关闭Redis连接\n */ async function closeRedis() {\n    if (redisClient) {\n        await redisClient.quit();\n        redisClient = null;\n        console.log('✅ Redis连接已关闭');\n    }\n}\n// 创建类似egg的app.redis对象\nconst redis_client = {\n    get,\n    set,\n    del,\n    exists,\n    expire,\n    ttl,\n    incr,\n    decr,\n    hset,\n    hget,\n    hgetall,\n    lpush,\n    rpop,\n    lrange,\n    sendCommand,\n    ping,\n    healthCheck,\n    // 获取原始客户端\n    getClient: getRedisClient\n};\nmodule.exports = {\n    initRedis,\n    redis: redis_client,\n    healthCheck,\n    closeRedis\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/redis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "ioredis":
/*!**************************!*\
  !*** external "ioredis" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("ioredis");

/***/ }),

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.4.5_react-dom@19.1.0_react@19.1.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffb%2Ffbremeber%2Froute&page=%2Fapi%2Ffb%2Ffbremeber%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffb%2Ffbremeber%2Froute.js&appDir=I%3A%5Cworkspace%5Cnextjs-backend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=I%3A%5Cworkspace%5Cnextjs-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();