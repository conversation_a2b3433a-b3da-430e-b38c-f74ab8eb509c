/**
 * 自定义Next.js服务器
 * 集成WebSocket服务器
 */

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const { app } = require('./src/lib/app');
const { initWebSocketServer } = require('./src/lib/websocket');
const { getConfig } = require('./src/lib/config');

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || 'localhost';
const port = process.env.PORT || 3000;

// 创建Next.js应用
const nextApp = next({ dev, hostname, port });
const handle = nextApp.getRequestHandler();

nextApp.prepare().then(async () => {
  try {
    // 初始化应用（数据库、Redis等）
    await app.init();
    console.log('✅ 应用初始化完成');

    // 创建HTTP服务器
    const server = createServer(async (req, res) => {
      try {
        const parsedUrl = parse(req.url, true);
        await handle(req, res, parsedUrl);
      } catch (err) {
        console.error('❌ 请求处理错误:', err);
        res.statusCode = 500;
        res.end('Internal Server Error');
      }
    });

    // 初始化WebSocket服务器
    initWebSocketServer(server);

    // 启动服务器
    server.listen(port, hostname, () => {
      console.log(`🚀 服务器启动成功:`);
      console.log(`   - HTTP: http://${hostname}:${port}`);
      console.log(`   - WebSocket: ws://${hostname}:${port}/socket.io/`);
      console.log(`   - 环境: ${dev ? 'development' : 'production'}`);
      
      // 显示WebSocket命名空间
      const wsConfig = getConfig('websocket');
      console.log(`   - WebSocket命名空间:`, wsConfig.namespaces.join(', '));
    });

    // 优雅关闭
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

    async function gracefulShutdown() {
      console.log('🔄 开始优雅关闭...');
      
      server.close(async () => {
        try {
          await app.close();
          console.log('✅ 服务器已优雅关闭');
          process.exit(0);
        } catch (error) {
          console.error('❌ 关闭过程中出错:', error);
          process.exit(1);
        }
      });
    }

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
});
