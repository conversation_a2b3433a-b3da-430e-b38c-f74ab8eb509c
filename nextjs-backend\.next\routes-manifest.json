{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/fbtimu", "destination": "/api/fb/timu", "regex": "^\\/fbtimu(?:\\/)?$", "check": true}, {"source": "/fbgettimu", "destination": "/api/fb/timu", "regex": "^\\/fbgettimu(?:\\/)?$", "check": true}, {"source": "/fbgethtimu", "destination": "/api/fb/gethtimu", "regex": "^\\/fbgethtimu(?:\\/)?$", "check": true}, {"source": "/gethtimu", "destination": "/api/fb/gethtimu", "regex": "^\\/gethtimu(?:\\/)?$", "check": true}, {"source": "/pinglun", "destination": "/api/fb/pinglun", "regex": "^\\/pinglun(?:\\/)?$", "check": true}, {"source": "/fbpl", "destination": "/api/fb/pinglun", "regex": "^\\/fbpl(?:\\/)?$", "check": true}, {"source": "/gettimurank", "destination": "/api/fb/gettimurank", "regex": "^\\/gettimurank(?:\\/)?$", "check": true}, {"source": "/fbsubmit", "destination": "/api/fb/fbsubmit", "regex": "^\\/fbsubmit(?:\\/)?$", "check": true}, {"source": "/fbremeber", "destination": "/api/fb/fbremeber", "regex": "^\\/fbremeber(?:\\/)?$", "check": true}, {"source": "/fbrecoverpage", "destination": "/api/fb/fbrecoverpage", "regex": "^\\/fbrecoverpage(?:\\/)?$", "check": true}, {"source": "/setnull", "destination": "/api/fb/setnull", "regex": "^\\/setnull(?:\\/)?$", "check": true}, {"source": "/updateidsnull", "destination": "/api/fb/updateidsnull", "regex": "^\\/updateidsnull(?:\\/)?$", "check": true}, {"source": "/showip", "destination": "/api/home/<USER>", "regex": "^\\/showip(?:\\/)?$", "check": true}, {"source": "/gitpull", "destination": "/api/home/<USER>", "regex": "^\\/gitpull(?:\\/)?$", "check": true}, {"source": "/button", "destination": "/api/home/<USER>", "regex": "^\\/button(?:\\/)?$", "check": true}, {"source": "/clashsw", "destination": "/api/home/<USER>", "regex": "^\\/clashsw(?:\\/)?$", "check": true}, {"source": "/health", "destination": "/api/home/<USER>", "regex": "^\\/health(?:\\/)?$", "check": true}, {"source": "/test-db", "destination": "/api/test-db", "regex": "^\\/test-db(?:\\/)?$", "check": true}, {"source": "/websocket-test", "destination": "/api/websocket/test", "regex": "^\\/websocket-test(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": []}