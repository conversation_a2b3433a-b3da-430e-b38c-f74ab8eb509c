"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hasown@2.0.2";
exports.ids = ["vendor-chunks/hasown@2.0.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/hasown@2.0.2/node_modules/hasown/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/.pnpm/hasown@2.0.2/node_modules/hasown/index.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar call = Function.prototype.call;\nvar $hasOwn = Object.prototype.hasOwnProperty;\nvar bind = __webpack_require__(/*! function-bind */ \"(rsc)/./node_modules/.pnpm/function-bind@1.1.2/node_modules/function-bind/index.js\");\n\n/** @type {import('.')} */\nmodule.exports = bind.call(call, $hasOwn);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vaGFzb3duQDIuMC4yL25vZGVfbW9kdWxlcy9oYXNvd24vaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLFdBQVcsbUJBQU8sQ0FBQyx5R0FBZTs7QUFFbEMsV0FBVyxhQUFhO0FBQ3hCIiwic291cmNlcyI6WyJJOlxcd29ya3NwYWNlXFxuZXh0anMtYmFja2VuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcaGFzb3duQDIuMC4yXFxub2RlX21vZHVsZXNcXGhhc293blxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgY2FsbCA9IEZ1bmN0aW9uLnByb3RvdHlwZS5jYWxsO1xudmFyICRoYXNPd24gPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5O1xudmFyIGJpbmQgPSByZXF1aXJlKCdmdW5jdGlvbi1iaW5kJyk7XG5cbi8qKiBAdHlwZSB7aW1wb3J0KCcuJyl9ICovXG5tb2R1bGUuZXhwb3J0cyA9IGJpbmQuY2FsbChjYWxsLCAkaGFzT3duKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/hasown@2.0.2/node_modules/hasown/index.js\n");

/***/ })

};
;