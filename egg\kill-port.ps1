# 强制终端编码为 GBK（解决中文乱码）
param(
    [Parameter(Mandatory=$true)]
    [int]$Port,
    
    [Parameter(Mandatory=$false)]
    [string]$Name = "App"
)

[Console]::OutputEncoding = [System.Text.Encoding]::GetEncoding("GBK")

Write-Host "[$Name] 🔍 检查端口 $Port..." -ForegroundColor Cyan

try {
    $processes = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | 
                 Select-Object -ExpandProperty OwningProcess -Unique

    if ($processes) {
        foreach ($procId in $processes) {  # 替换 $pid → $procId
            try {
                $process = Get-Process -Id $procId -ErrorAction SilentlyContinue  # 同步替换
                if ($process) {
                    Write-Host "[$Name] 🎯 发现占用进程: $($process.ProcessName) (PID: $procId)" -ForegroundColor Yellow
                    Stop-Process -Id $procId -Force -ErrorAction Stop  # 同步替换
                    Write-Host "[$Name] ✅ 成功终止进程 $procId" -ForegroundColor Green
                } else {
                    Write-Host "[$Name] ⚠️ 进程 $procId 已不存在" -ForegroundColor Yellow
                }
            }
            catch {
                Write-Host "[$Name] ❌ 终止进程 $procId 失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        Write-Host "[$Name] 🎉 端口 $Port 占用已解除" -ForegroundColor Green
    } else {
        Write-Host "[$Name] ✨ 端口 $Port 未被占用" -ForegroundColor Green
    }
}
catch {
    Write-Host "[$Name] ❌ 检查端口时出错: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

exit 0