package handlers

import (
	"fiber-app/services" // Import the services package
	"github.com/gofiber/fiber/v2"
)

// FBHandler holds the FBService instance.
type FBHandler struct {
	fbService *services.FBService
}

// NewFBHandler creates a new FBHandler.
func NewFBHandler(fbService *services.FBService) *FBHandler {
	return &FBHandler{fbService: fbService}
}

// GetTimu handles the /egg/fbtimu API endpoint.
func (h *FBHandler) GetTimu(c *fiber.Ctx) error {
	// Extract query parameters
	per := c.Query("per")
	page := c.Query("page")
	id := c.Query("id")
	timuType := c.Query("type")
	z := c.Query("z")
	b := c.Query("b")
	f := c.Query("f")
	o := c.Query("o")
	ids := c.Query("ids")
	biao := c.Query("biao")
	t := c.Query("t")
	zql := c.Query("zql")
	kjid := c.Query("kjid")
	fast := c.Query("fast")

	// Call the service layer to process the request
	data, err := h.fbService.ProcessTimu(per, page, id, timuType, z, b, f, o, ids, biao, t, zql, kjid, fast)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"code":    -1,
			"message": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"code":    0,
		"message": "success",
		"data":    data,
