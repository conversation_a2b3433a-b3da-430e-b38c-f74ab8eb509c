package services

import (
	"fiber-app/cache"
	"fiber-app/repository"
	"net/http"
)

type FBService struct {
	repo       *repository.DBRepository
	cache      *cache.RedisCache
	httpClient *http.Client
}

func NewFBService(repo *repository.DBRepository, cache *cache.RedisCache, client *http.Client) *FBService {
	return &FBService{
		repo:       repo,
		cache:      cache,
		httpClient: client,
	}
}

// Add other business logic functions from fb.js here
