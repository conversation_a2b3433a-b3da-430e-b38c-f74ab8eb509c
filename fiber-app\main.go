package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"fiber-app/config"
	"fiber-app/repository" // Import the new repository package

	_ "github.com/go-sql-driver/mysql" // Import MySQL driver
	"github.com/gofiber/fiber/v2"
)

func main() {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "default"
	}

	appConfig, err := config.LoadConfig(env)
	if err != nil {
		log.Fatalf("Error loading configuration: %v", err)
	}

	// Database connection
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		appConfig.Database.User,
		appConfig.Database.Password,
		appConfig.Database.Host,
		appConfig.Database.Port,
		appConfig.Database.Name,
	)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Error opening database connection: %v", err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		log.Fatalf("Error connecting to the database: %v", err)
	}
	log.Println("Successfully connected to the database!")

	dbRepo := repository.NewDBRepository(db)

	app := fiber.New()

	app.Get("/", func(c *fiber.Ctx) error {
		// Example usage of dbRepo (for testing purposes)
		// You can remove this later
		_, err := dbRepo.Find("SELECT 1")
		if err != nil {
			return c.SendString(fmt.Sprintf("Hello, Fiber! App Port: %d, DB Test Error: %v", appConfig.App.Port, err))
		}
		return c.SendString(fmt.Sprintf("Hello, Fiber! App Port: %d, DB Connected: true", appConfig.App.Port))
	})

	log.Printf("Starting server on port %d", appConfig.App.Port)
	log.Fatal(app.Listen(fmt.Sprintf(":%d", appConfig.App.Port)))
}
