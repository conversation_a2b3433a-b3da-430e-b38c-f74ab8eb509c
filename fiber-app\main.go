package main

import (
	"database/sql"
	"fmt"
	"log"
	"net/http" // Import net/http
	"os"
	"time"

	"fiber-app/cache"
	"fiber-app/config"
	"fiber-app/handlers"
	"fiber-app/repository"
	"fiber-app/services" // Import the services package

	_ "github.com/go-sql-driver/mysql" // Import MySQL driver
	"github.com/gofiber/fiber/v2"
)

func main() {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "default"
	}

	appConfig, err := config.LoadConfig(env)
	if err != nil {
		log.Fatalf("Error loading configuration: %v", err)
	}

	// Database connection
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		appConfig.Database.User,
		appConfig.Database.Password,
		appConfig.Database.Host,
		appConfig.Database.Port,
		appConfig.Database.Name,
	)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Error opening database connection: %v", err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		log.Fatalf("Error connecting to the database: %v", err)
	}
	log.Println("Successfully connected to the database!")

	dbRepo := repository.NewDBRepository(db)

	// Redis connection
	redisCache := cache.NewRedisCache(
		appConfig.Redis.Addr,
		appConfig.Redis.Password,
		appConfig.Redis.DB,
	)
	err = redisCache.Ping()
	if err != nil {
		log.Fatalf("Error connecting to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis!")

	// Initialize HTTP client
	httpClient := &http.Client{Timeout: 10 * time.Second}

	// Initialize services
	fbService := services.NewFBService(dbRepo, redisCache, httpClient)

	// Initialize handlers
	fbHandler := handlers.NewFBHandler(fbService)

	app := fiber.New()

	// Register API routes
	app.Get("/fbtimu", fbHandler.GetTimu)
	app.Post("/fbsubmit", fbHandler.SubmitAnswer)

	app.Get("/", func(c *fiber.Ctx) error {
		// Example usage of dbRepo and redisCache (for testing purposes)
		// You can remove this later
		_, err := dbRepo.Find("SELECT 1")
		if err != nil {
			return c.SendString(fmt.Sprintf("Hello, Fiber! App Port: %d, DB Test Error: %v", appConfig.App.Port, err))
		}

		testKey := "test_key"
		testValue := "test_value"
		err = redisCache.SetEx(testKey, testValue, 10*time.Second)
		if err != nil {
			return c.SendString(fmt.Sprintf("Hello, Fiber! App Port: %d, DB Connected: true, Redis Set Error: %v", appConfig.App.Port, err))
		}

		var retrievedValue string
		err = redisCache.Get(testKey, &retrievedValue)
		if err != nil {
			return c.SendString(fmt.Sprintf("Hello, Fiber! App Port: %d, DB Connected: true, Redis Get Error: %v", appConfig.App.Port, err))
		}

		return c.SendString(fmt.Sprintf("Hello, Fiber! App Port: %d, DB Connected: true, Redis Connected: true, Retrieved: %s", appConfig.App.Port, retrievedValue))
	})

	log.Printf("Starting server on port %d", appConfig.App.Port)
	log.Fatal(app.Listen(fmt.Sprintf(":%d", appConfig.App.Port)))
}
