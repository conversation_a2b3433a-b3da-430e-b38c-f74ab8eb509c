package main

import (
	"context"
	"fmt"

	"github.com/redis/go-redis/v9"
)

// InitRedis 初始化Redis客户端
func InitRedis(cfg *Config) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})
	// 测试连接
	if err := rdb.Ping(context.Background()).Err(); err != nil {
		return nil, fmt.Errorf("Redis连接失败: %w", err)
	}
	return rdb, nil
}
