/** @type {import('next').NextConfig} */
const nextConfig = {
  // 纯后端配置 - 使用新的配置项
  serverExternalPackages: ['mysql2', 'ioredis', 'ws'],

  // 输出配置
  output: 'standalone',

  // 禁用不必要的功能
  images: {
    unoptimized: true,
  },

  // 路由重写 - 兼容eggjs路由
  async rewrites() {
    return [
      // FB相关API路由重写
      {
        source: '/fbtimu',
        destination: '/api/fb/timu',
      },
      {
        source: '/pinglun',
        destination: '/api/fb/pinglun',
      },
      {
        source: '/gettimurank',
        destination: '/api/fb/gettimurank',
      },
      {
        source: '/gethtimu',
        destination: '/api/fb/gethtimu',
      },
      {
        source: '/fbsubmit',
        destination: '/api/fb/fbsubmit',
      },
      {
        source: '/fbremeber',
        destination: '/api/fb/fbremeber',
      },
      {
        source: '/fbrecoverpage',
        destination: '/api/fb/fbrecoverpage',
      },
      {
        source: '/setnull',
        destination: '/api/fb/setnull',
      },
      {
        source: '/updateidsnull',
        destination: '/api/fb/updateidsnull',
      },
      // Home相关API路由重写
      {
        source: '/showip',
        destination: '/api/home/<USER>',
      },
      {
        source: '/gitpull',
        destination: '/api/home/<USER>',
      },
      {
        source: '/button',
        destination: '/api/home/<USER>',
      },
      {
        source: '/clashsw',
        destination: '/api/home/<USER>',
      },
      {
        source: '/health',
        destination: '/api/home/<USER>',
      },
    ];
  },

  // 实验性功能
  experimental: {
    // 其他实验性配置可以在这里添加
  },
};

module.exports = nextConfig;
