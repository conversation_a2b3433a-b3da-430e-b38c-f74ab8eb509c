/**
 * WebSocket测试API
 * 用于测试WebSocket服务器功能
 */

import { NextResponse } from 'next/server';
import { broadcastToNamespace, getStats, healthCheck } from '@/lib/websocket';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'stats';
    
    switch (action) {
      case 'stats':
        // 获取WebSocket连接统计
        const stats = getStats();
        return NextResponse.json(stats);
        
      case 'health':
        // WebSocket健康检查
        const health = healthCheck();
        return NextResponse.json(health);
        
      case 'broadcast':
        // 广播测试消息
        const namespace = searchParams.get('namespace') || '/test';
        const message = searchParams.get('message') || 'Test broadcast message';
        
        broadcastToNamespace(namespace, 'test-broadcast', {
          message,
          timestamp: new Date().toISOString(),
          from: 'API'
        });
        
        return NextResponse.json({
          success: true,
          message: `消息已广播到 ${namespace}`,
          data: { namespace, message }
        });
        
      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['stats', 'health', 'broadcast']
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ WebSocket测试API错误:', error.message);
    return NextResponse.json({
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action, namespace, event, data } = body;
    
    switch (action) {
      case 'broadcast':
        if (!namespace || !event) {
          return NextResponse.json({
            error: 'namespace和event参数必需'
          }, { status: 400 });
        }
        
        broadcastToNamespace(namespace, event, data || {});
        
        return NextResponse.json({
          success: true,
          message: `事件 ${event} 已广播到 ${namespace}`,
          data: { namespace, event, data }
        });
        
      case 'simulate-thinking':
        // 模拟思考过程
        broadcastToNamespace('/thinkprocess', 'thinking', {
          content: '这是一个模拟的思考过程...',
          timestamp: new Date().toISOString()
        });
        
        setTimeout(() => {
          broadcastToNamespace('/thinkprocess', 'answer', {
            content: '模拟答案：这是测试结果',
            timestamp: new Date().toISOString()
          });
        }, 2000);
        
        return NextResponse.json({
          success: true,
          message: '模拟思考过程已启动'
        });
        
      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['broadcast', 'simulate-thinking']
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ WebSocket测试API错误:', error.message);
    return NextResponse.json({
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
