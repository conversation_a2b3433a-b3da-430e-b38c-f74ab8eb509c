package models

// TimuParams holds all the query parameters for the timu logic.
// By placing it in the models package, it can be shared between handlers and services.
type TimuParams struct {
	ID       string `json:"id"`
	Per      string `json:"per"`
	Page     string `json:"page"`
	Type     string `json:"type"`
	Z        string `json:"z"`
	B        string `json:"b"`
	T        string `json:"t"`
	Gen      string `json:"gen"`
	IDs      string `json:"ids"`
	Kjid     string `json:"kjid"`
	F        string `json:"f"`
	O        string `json:"o"`
	Q        string `json:"q"`
	Biao     string `json:"biao"`
	ParentID string `json:"parentid"`
	Fast     string `json:"fast"`
	ZQL      string `json:"zql"`
}
